package com.gg.gapo.home.presentation

import android.Manifest
import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings.ACTION_MANAGE_APP_USE_FULL_SCREEN_INTENT
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.deeplinkdispatch.DeepLink
import com.gg.gapo.R
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.eventbus.billing.BillingErrorBusEvent
import com.gg.gapo.core.eventbus.friend.FriendHaveNewRequestBusEvent
import com.gg.gapo.core.eventbus.home.HomeUnreadFeedFollowBusEvent
import com.gg.gapo.core.eventbus.home.HomeUnreadNotificationBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.feed.common.provider.FeedSharedImageLoaderProvider
import com.gg.gapo.core.feed.common.provider.FeedSharedRecyclerViewPoolProvider
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.WebDeepLink
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthSecurityDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthUpdateProfileDeepLink
import com.gg.gapo.core.navigation.deeplink.billing.BillingNoticeDeepLink
import com.gg.gapo.core.navigation.deeplink.home.HomeDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceInvitationDeepLink
import com.gg.gapo.core.navigation.system.openAppSystemNotificationSettings
import com.gg.gapo.core.notification.GapoNotificationManager
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.ui.toast.GapoToast.showOnTop
import com.gg.gapo.core.utilities.billing.GapoBillingErrorType
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.di.qualifier.GapoConstantQualifier
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.lifecycle.GapoDefaultLifecycleObserver
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.recyclerview.NoLimitRecycledViewPool
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.core.utilities.view.tablayout.GapoAwesomeOnTabSelectedListener
import com.gg.gapo.core.utilities.view.viewpager.findCurrentFragment
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.domain.model.FeatureModel
import com.gg.gapo.core.workspace.domain.model.WorkspaceModel
import com.gg.gapo.core.workspace.domain.model.WorkspaceSwitchedFrom
import com.gg.gapo.core.workspace.eventbus.NavigateWorkspaceInvitationsBusEvent
import com.gg.gapo.core.workspace.eventbus.WorkspaceSwitchedBusEvent
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.databinding.HomeActivityBinding
import com.gg.gapo.deeplink.event.DeeplinkHandleApproveEventBus
import com.gg.gapo.deeplink.model.EventResult
import com.gg.gapo.feature.auth.presentation.login.GapoSDKAuthentication
import com.gg.gapo.feature.auth.presentation.login.SDKAuthListener
import com.gg.gapo.feature.feed.follow.presentation.FeedFollowFragment
import com.gg.gapo.feature.feed.follow.presentation.eventbus.FeedFollowReloadDataBusEvent
import com.gg.gapo.feature.feed.follow.presentation.eventbus.FeedFollowScrollToTopBusEvent
import com.gg.gapo.feature.group.presentation.feed.groups.FeedGroupsFragment
import com.gg.gapo.feature.group.presentation.feed.groups.eventbus.FeedGroupsReloadDataBusEvent
import com.gg.gapo.feature.group.presentation.feed.groups.eventbus.FeedGroupsScrollToTopBusEvent
import com.gg.gapo.feature.messenger.presentation.bot.BotListBottomSheetFragment
import com.gg.gapo.feature.messenger.presentation.conversation.ConversationFragment
import com.gg.gapo.feature.notification.presentation.eventbus.NotificationReloadDataBusEvent
import com.gg.gapo.feature.notification.presentation.eventbus.NotificationScrollToTopBusEvent
import com.gg.gapo.feature.notification.presentation.notification.NotificationFragment
import com.gg.gapo.feature.setting.presentation.menu.MenuFragment
import com.gg.gapo.flutterx.utils.GapoFlutterFragmentFactory
import com.gg.gapo.flutterx.utils.GapoFlutterFragmentInteractor
import com.gg.gapo.home.domain.notification.model.AskNotificationPermissionContext
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter.Companion.CALENDAR_POSITION
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter.Companion.CONVERSATION_POSITION
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter.Companion.FEED_FOLLOW_POSITION
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter.Companion.FEED_GROUPS_POSITION
import com.gg.gapo.home.presentation.adapter.HomePagerAdapter.Companion.NOTIFICATION_POSITION
import com.gg.gapo.home.presentation.dialog.ForceUpdateDialog
import com.gg.gapo.home.presentation.dialog.ToggleFeaturesDialog
import com.gg.gapo.home.presentation.launcher.WorkspaceIconLauncher
import com.gg.gapo.home.presentation.launcher.setComponentEnabledSetting
import com.gg.gapo.home.presentation.lifecycle.HomeUserMeLifecycleRegistry
import com.gg.gapo.home.presentation.record.YodyRecordDialogFragment
import com.gg.gapo.home.presentation.viewmodel.HomeViewModel
import com.gg.gapo.messenger.presentation.events.ConversationDefaultUnRead
import com.gg.gapo.messenger.presentation.events.MessengerConversationReloadBusEvent
import com.gg.gapo.messenger.presentation.events.MessengerConversationScrollToTopBusEvent
import com.gg.gapo.notification.fcm.event.OnClickNotificationInAppBusEvent
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.loadKoinModules
import org.koin.core.context.unloadKoinModules
import org.koin.core.qualifier.named
import org.koin.dsl.module

@AppDeepLink(
    value = [
        "home", "feed/follow", // một vài noti có deeplink nav về feed
        "messenger", "notification", "calendar"
    ]
)
@WebDeepLink(
    value = ["", "/", "group", "group/", "messenger/", "messenger", "notification", "notification/", "calendar", "calendar/"]
)
class HomeActivity :
    GapoThemeBaseActivity(),
    FeedSharedRecyclerViewPoolProvider,
    FeedSharedImageLoaderProvider {

    /* Shared for all Feed Fragments in Home Screen */
    override val feedPostRecyclerViewPool: NoLimitRecycledViewPool
        get() = _feedPostRecyclerViewPool

    override val feedNestedRecyclerViewViewPool: NoLimitRecycledViewPool
        get() = _feedNestedRecyclerViewViewPool

    override val sharedFeedImageLoader: FeedImageLoader
        get() = glideRequests

    private val _feedPostRecyclerViewPool = NoLimitRecycledViewPool()

    private val _feedNestedRecyclerViewViewPool = NoLimitRecycledViewPool()

    private lateinit var glideRequests: GlideRequests

    private fun createSharedFeedProvider() {
        glideRequests = GapoGlide.with(this)
    }
    /* Shared for all Feed Fragments in Home Screen */

    private val notificationManager by inject<GapoNotificationManager>()

    private val workspaceManager by inject<WorkspaceManager>()

    private lateinit var binding: HomeActivityBinding

    private lateinit var pagerAdapter: HomePagerAdapter

    private val homeViewModel by viewModel<HomeViewModel>()

    private val homeUserMeLifecycleRegistry by lazy(LazyThreadSafetyMode.NONE) {
        HomeUserMeLifecycleRegistry(this)
    }

    private val fragments: MutableList<Fragment>
        get() = mutableListOf<Fragment>().apply {
            val currentWorkspace = workspaceManager.currentWorkspace ?: workspaceManager.currentWorkspaceCache
            val features = currentWorkspace?.features ?: FeatureModel.DEFAULT
            add(FeedFollowFragment.createInstance())
            if (features.isEnable(Feature.GROUP)) add(FeedGroupsFragment.createInstance())
            if (features.isEnable(Feature.MESSENGER)) add(ConversationFragment.newInstance())
            add(NotificationFragment.newInstance())
            if (features.isEnable(Feature.CALENDAR)) add(GapoFlutterFragmentFactory.createCalendarFragment())
            add(MenuFragment.newInstance())
        }

    private val appDomainModule by lazy {
        module {
            single(qualifier = named(GapoConstantQualifier.SUB_DOMAIN_BASE_URL)) {
                "https://${workspaceManager.currentWorkspace?.appDomain}/"
            }
            single(qualifier = named(GapoConstantQualifier.IS_ENABLE_CAPTURE_SCREEN)) {
                workspaceManager.currentWorkspace?.features?.isEnable(Feature.CAPTURE_SCREEN)
                    ?: workspaceManager.currentWorkspaceCache?.features?.isEnable(Feature.CAPTURE_SCREEN) ?: true
            }
            single(qualifier = named(GapoConstantQualifier.IS_ENABLE_DOWNLOAD)) {
                workspaceManager.currentWorkspace?.features?.isEnable(Feature.DOWNLOAD)
                    ?: workspaceManager.currentWorkspaceCache?.features?.isEnable(Feature.DOWNLOAD) ?: true
            }
        }
    }

    private val onResumeLifecycleObserver = object : GapoDefaultLifecycleObserver() {
        override fun onResume(owner: LifecycleOwner) {
            homeViewModel.checkUpdate()
            homeViewModel.fetchUnreadCount()
            trackingHomeChildScreen(binding.mainViewPager.currentItem)
        }
    }

    private val postNotificationPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) {
            /*Do Nothing*/
        }

    private val pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            when (position) {
                FEED_FOLLOW_POSITION -> {
                    GAPOAnalytics.getInstance(this@HomeActivity)
                        .logEventExploreFollow(screenName = "")
                }

                FEED_GROUPS_POSITION -> {
                    GAPOAnalytics.getInstance(this@HomeActivity)
                        .logEventExploreGroup(screenName = "")
                }

                CONVERSATION_POSITION -> {
                    GAPOAnalytics.getInstance(this@HomeActivity)
                        .logEventExploreChat(screenName = "")
                }

                NOTIFICATION_POSITION -> {
                    GAPOAnalytics.getInstance(this@HomeActivity)
                        .logEventExploreNotification(screenName = "")
                }
            }
            trackingHomeChildScreen(position)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        checkIntent(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(0, 0)
        binding = HomeActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        observeLiveData()

        loadKoinModules(appDomainModule)

        if (intent.hasAndTrueExtra(HomeDeepLink.NEED_FETCH_USER_ME_EXTRA)) {
            // đảm bảo chỉ gọi 1 lần duy nhất khi vào app
            // vì khi đổi Activity Resource có thể bị recreate
            intent.removeExtra(HomeDeepLink.NEED_FETCH_USER_ME_EXTRA)
            homeViewModel.fetchUsersMe()
        }

        createSharedFeedProvider()

        setupTabAndViewPager()

        checkIntent(intent)

        // Enable khi không xin được quyền full screen intent mặc định khi tải app từ store
        // checkFullScreenIntentPermission()

        if (homeViewModel.shouldAskNotificationPermission(AskNotificationPermissionContext.HOME)) {
            askNotificationPermission()
        }
    }

    private fun checkFullScreenIntentPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            if (!notificationManager.canUseFullScreenIntent()) {
                startActivity(
                    Intent(ACTION_MANAGE_APP_USE_FULL_SCREEN_INTENT).apply {
                        data = Uri.fromParts("package", applicationContext.packageName, null)
                    }
                )
            }
        }
    }

    private fun observeLiveData() {
        homeViewModel.changeLogLiveData.observe(
            this,
            EventObserver {
                val version = it.version
                val forceUpdateDialog = ForceUpdateDialog(this, glideRequests, version)
                if (version.isForceUpdate || homeViewModel.shouldShowUpdateApp(version.appVersion)) {
                    forceUpdateDialog.show()
                }
            }
        )

        homeViewModel.moveToUpdateProfileEventLiveData.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner,
            EventObserver {
                navByDeepLink(AuthUpdateProfileDeepLink())
                finishAffinity()
            }
        )

        homeViewModel.moveToInvitationWorkspaceEventLiveData.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner,
            EventObserver {
                navToListWorkspace()
                finishAffinity()
            }
        )

        homeViewModel.workspaceManager.currentWorkspaceLiveData.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner
        ) {
            setUpFloatingButtonBotList(it)
        }

        homeViewModel.showPopupForceChangePassword.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner,
            EventObserver {
                if (it) {
                    navByDeepLink(AuthSecurityDeepLink())
                }
            }
        )

        homeViewModel.showPopupToggleFeatures.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner
        ) {
            val toggleFeatureDialog = ToggleFeaturesDialog(this, it)
            toggleFeatureDialog.show()
        }

        homeViewModel.shownYodyRecordPopupLiveData.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner,
            EventObserver {
                showDialogRemindYody()
            }
        )

        homeViewModel.handleTaskRequestStatusLiveData.observe(
            homeUserMeLifecycleRegistry.lifeCycleOwner,
            EventObserver {
                when (it) {
                    is EventResult.Success -> {
                        GapoToast.makePositive(this, it.data).showOnTop()
                    }

                    is EventResult.Error -> {
                        GapoToast.makeNegative(this, it.msg).showOnTop()
                    }

                    EventResult.Loading -> {
                    }
                }
            }
        )
    }

    private fun setUpFloatingButtonBotList(workspaceModel: WorkspaceModel?) {
        val isFeatureEnabled = workspaceManager.currentWorkspace?.features?.isEnable(Feature.MESSENGER) ?: true
        binding.floatingButtonBotList.visibility =
            if ((binding.mainViewPager.currentItem == FEED_FOLLOW_POSITION || binding.mainViewPager.currentItem == FEED_GROUPS_POSITION) && workspaceModel?.floatButton != null && isFeatureEnabled) {
                glideRequests.load(workspaceModel.floatButton!!.icon)
                    .circleCrop()
                    .into(binding.floatingButtonBotList)
                binding.floatingButtonBotList.setDebouncedClickListener {
                    BotListBottomSheetFragment.showList(fragmentManager = supportFragmentManager)
                }
                View.VISIBLE
            } else {
                View.GONE
            }
    }

    override fun onBackPressed() {
        val viewPager = binding.mainViewPager
        val currentPosition = viewPager.currentItem
        when {
            currentPosition == CALENDAR_POSITION -> {
                // Vì GapoCalendarFragment là host của Flutter Screen nên cần check để xử lý.
                val calendarFragment =
                    viewPager.findCurrentFragment(supportFragmentManager) as? GapoFlutterFragmentInteractor
                // Nếu đang trong luồng Flutter thì sẽ back cho đến khi gặp initial route.
                // Nếu đang ở initial route thì GapoCalendarFragment sẽ fire HomeOpenFeedsFollowTabBusEvent để back về FEEDS_FOLLOW_POSITION.
                calendarFragment?.onBackPressed()
            }

            currentPosition != FEED_FOLLOW_POSITION -> {
                switchFeedFollowTab()
            }

            else -> {
                super.onBackPressed()
            }
        }
    }

    override fun onStart() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(onResumeLifecycleObserver)
        super.onStart()
        registerEventBus()
        homeViewModel.fetchUnreadCountInterval()
        GAPOAnalytics.getInstance(this).logUserPropertiesAndPermissionApp()
    }

    override fun onDestroy() {
        checkChangeLauncherApp()
        ProcessLifecycleOwner.get().lifecycle.removeObserver(onResumeLifecycleObserver)
        binding.mainViewPager.unregisterOnPageChangeCallback(pageChangeCallback)
        super.onDestroy()
        unregisterEventBus()
        binding.mainViewPager.adapter = null
    }

    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (binding.mainViewPager.currentItem) {
            CALENDAR_POSITION -> {
                val calendarFragment =
                    binding.mainViewPager.findCurrentFragment(supportFragmentManager)
                calendarFragment?.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onUnreadFeedFollowCountEvent(event: HomeUnreadFeedFollowBusEvent) {
        setUnreadCountBadge<FeedFollowFragment>(event.count)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onUnreadNotificationCountEvent(event: HomeUnreadNotificationBusEvent) {
        setUnreadCountBadge<NotificationFragment>(event.count)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onUnreadChatConversationCountEvent(event: ConversationDefaultUnRead) {
        setUnreadCountBadge<ConversationFragment>(event.count)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onHaveNewFriendRequestEvent(event: FriendHaveNewRequestBusEvent) {
        setUnreadCountBadge<MenuFragment>(event.count)
    }

    /**
     * <AUTHOR>
     * setup lại các fragments để apply workspace changed
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onSwitchedWorkspace(event: WorkspaceSwitchedBusEvent) {
        unloadKoinModules(appDomainModule)
        loadKoinModules(appDomainModule)
        recreateViewPager()
        setupBottomTabs()
        TabLayoutMediator(
            binding.bottomTab,
            binding.mainViewPager,
            false,
            false
        ) { tab, position ->
            when (fragments[position]) {
                is FeedFollowFragment -> tab.setIcon(R.drawable.home_ic_feed_bottom_state)
                is FeedGroupsFragment -> tab.setIcon(R.drawable.home_ic_group_bottom_state)
                is ConversationFragment -> tab.setIcon(R.drawable.home_ic_chat_bottom_state)
                is NotificationFragment -> tab.setIcon(R.drawable.home_ic_notification_bottom_state)
                is MenuFragment -> tab.setIcon(R.drawable.home_ic_menu_bottom_state)
                else -> tab.setIcon(R.drawable.home_ic_calendar_bottom_state)
            }
        }.attach()

        when (event.from) {
            WorkspaceSwitchedFrom.NOTIFICATION -> switchNotificationTab()
            WorkspaceSwitchedFrom.MENU -> switchMenuTab()
            else -> {}
        }

        if (homeViewModel.approveRequestHolder.requestId.isNotEmpty()) {
            homeViewModel.approveRequestHolder.run {
                homeViewModel.handleTaskRequestState(
                    id = requestId,
                    stateId = workflowId,
                    status = status,
                    workspaceId = workspaceId,
                    flowType = isAdvanceWorkflow
                )
                homeViewModel.approveRequestHolder = DeeplinkHandleApproveEventBus()
            }
        }
        homeViewModel.notifyToggleFeatures()
    }

    /**
     * <AUTHOR>
     * navigate user tới màn ds workspace
     * được trigger khi renew token và user chỉ còn các workspaces pending
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onNavigateWorkspaceInvitations(event: NavigateWorkspaceInvitationsBusEvent) {
        navToListWorkspace()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onBillingError(event: BillingErrorBusEvent) {
        val errorType = GapoBillingErrorType.getValue(event.errorCode) ?: return
        val intent = BillingNoticeDeepLink(
            options = GapoDeepLink.Options(
                bundle = BillingNoticeDeepLink.createBundle(errorType.value)
            )
        )
        navByDeepLink(intent)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onHandleApproveEvent(event: DeeplinkHandleApproveEventBus) {
        event.run {
            handleUrgentlyApprove(
                DeeplinkHandleApproveEventBus(
                    requestId = event.requestId,
                    tabId = event.tabId,
                    workflowId = event.workflowId,
                    status = event.status,
                    workspaceId = event.workspaceId,
                    isAdvanceWorkflow = event.isAdvanceWorkflow
                )
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onClickNotificationInAppBusEvent(event: OnClickNotificationInAppBusEvent) {
        notificationManager.onClickNotification(this, event.payload)
    }

    private fun setupTabAndViewPager() {
        setupViewPager()
        setupBottomTabs()

        TabLayoutMediator(
            binding.bottomTab,
            binding.mainViewPager,
            false,
            false
        ) { tab, position ->
            when (fragments[position]) {
                is FeedFollowFragment -> tab.setIcon(R.drawable.home_ic_feed_bottom_state)
                is FeedGroupsFragment -> tab.setIcon(R.drawable.home_ic_group_bottom_state)
                is ConversationFragment -> tab.setIcon(R.drawable.home_ic_chat_bottom_state)
                is NotificationFragment -> tab.setIcon(R.drawable.home_ic_notification_bottom_state)
                is MenuFragment -> tab.setIcon(R.drawable.home_ic_menu_bottom_state)
                else -> tab.setIcon(R.drawable.home_ic_calendar_bottom_state)
            }
        }.attach()
    }

    private fun setupBottomTabs() {
        binding.bottomTab.tabIconTint = null

        binding.bottomTab.addOnTabSelectedListener(object : GapoAwesomeOnTabSelectedListener() {
            override fun onScrollToTopScreenAt(tab: TabLayout.Tab) {
                when (fragments[tab.position]) {
                    is FeedFollowFragment -> FeedFollowScrollToTopBusEvent.postEvent()
                    is FeedGroupsFragment -> FeedGroupsScrollToTopBusEvent.postEvent()
                    is ConversationFragment -> MessengerConversationScrollToTopBusEvent.postEvent()
                    is NotificationFragment -> NotificationScrollToTopBusEvent().postEvent()
                    is MenuFragment -> {}
                    else -> {
                        val interactor =
                            binding.mainViewPager.findCurrentFragment(supportFragmentManager) as? GapoFlutterFragmentInteractor
                        interactor?.onScrollToTop()
                    }
                }
            }

            override fun onReloadDataScreenAt(tab: TabLayout.Tab) {
                when (fragments[tab.position]) {
                    is FeedFollowFragment -> FeedFollowReloadDataBusEvent.postEvent()
                    is FeedGroupsFragment -> FeedGroupsReloadDataBusEvent.postEvent()
                    is ConversationFragment -> MessengerConversationReloadBusEvent.postEvent()
                    is NotificationFragment -> NotificationReloadDataBusEvent().postEvent()
                    is MenuFragment -> {}
                    else -> {
                        val interactor =
                            binding.mainViewPager.findCurrentFragment(supportFragmentManager) as? GapoFlutterFragmentInteractor
                        interactor?.onReloadData()
                    }
                }
            }

            override fun onTabSelected(tab: TabLayout.Tab) {
                super.onTabSelected(tab)
                homeViewModel.checkShowYodyRecord()

                if (fragments[tab.position] is ConversationFragment) {
                    homeViewModel.saveTabPosition(tab.position)
                } else {
                    homeViewModel.saveTabPosition(FEED_FOLLOW_POSITION)
                }
                when (fragments[tab.position]) {
                    is FeedFollowFragment -> {
                        homeViewModel.checkForceChangePassword()
                        onUnreadFeedFollowCountEvent(HomeUnreadFeedFollowBusEvent(0))
                    }

                    is NotificationFragment -> {
                        if (homeViewModel.shouldAskNotificationPermission(
                                AskNotificationPermissionContext.NOTIFICATION_TAB
                            )
                        ) {
                            askNotificationPermission()
                        }
                    }
                }
                setUpFloatingButtonBotList(homeViewModel.workspaceManager.currentWorkspace)
            }
        })
    }

    @SuppressLint("WrongConstant")
    private fun setupViewPager() {
        binding.mainViewPager.isUserInputEnabled = false
        pagerAdapter = HomePagerAdapter(this, fragments)
        binding.mainViewPager.adapter = pagerAdapter
        binding.mainViewPager.setCurrentItem(homeViewModel.savedTabPosition, false)
        binding.mainViewPager.offscreenPageLimit = HomePagerAdapter.OFF_SCREEN_PAGE_LIMIT
        binding.mainViewPager.registerOnPageChangeCallback(pageChangeCallback)
    }

    /**
     * <AUTHOR>
     * <AUTHOR> add removeAllFragments để fix leak nhanh submit cho EP2 - auth/workspace v2
     * Cần refactor lại.
     * Có thể dùng Fragment làm host của ViewPager.
     * Mỗi khi cần recreate thì attach/detach lại Fragment.
     */
    private fun recreateViewPager() {
        pagerAdapter.removeAllFragments()
        val currentWorkspace = workspaceManager.currentWorkspace ?: workspaceManager.currentWorkspaceCache
        fragments.apply {
            clear()
            val features = currentWorkspace?.features ?: FeatureModel.DEFAULT
            add(FeedFollowFragment.createInstance())
            if (features.isEnable(Feature.GROUP)) add(FeedGroupsFragment.createInstance())
            if (features.isEnable(Feature.MESSENGER)) add(ConversationFragment.newInstance())
            add(NotificationFragment.newInstance())
            if (features.isEnable(Feature.CALENDAR)) add(GapoFlutterFragmentFactory.createCalendarFragment())
            add(MenuFragment.newInstance())
        }
        binding.mainViewPager.adapter = null
        binding.mainViewPager.adapter = HomePagerAdapter(this, fragments).also {
            pagerAdapter = it
        }
        binding.mainViewPager.adapter?.notifyDataSetChanged()
    }

    private fun checkIntent(intent: Intent) {
        if (intent.hasAndTrueExtra(HomeDeepLink.FEED_FOLLOW_SELECTED_EXTRA)) {
            switchFeedFollowTab()
        }

        val uri = try {
            Uri.parse(intent.extras?.getString(DeepLink.URI, "").orEmpty())
        } catch (e: Exception) {
            null
        }

        val lastPath = uri?.pathSegments?.lastOrNull().orEmpty().ifEmpty { uri?.authority }

        if (lastPath == "group" || intent.hasAndTrueExtra(HomeDeepLink.GROUPS_SELECTED_EXTRA)) {
            switchFeedGroupsTab()
        }

        if (lastPath == "messenger" || intent.hasAndTrueExtra(HomeDeepLink.MESSENGER_SELECTED_EXTRA)) {
            switchMessengerTab()
        }

        if (lastPath == "notification" || intent.hasAndTrueExtra(HomeDeepLink.NOTIFICATION_SELECTED_EXTRA)) {
            switchNotificationTab()
        }

        if (lastPath == "calendar" || intent.hasAndTrueExtra(HomeDeepLink.CALENDAR_SELECTED_EXTRA)) {
            switchCalendarTab()
        }

        if (intent.hasAndTrueExtra(HomeDeepLink.MENU_SELECTED_EXTRA)) {
            switchMenuTab()
        }

        if (intent.hasExtra(NOTIFICATION_PAYLOAD_EXTRA)) {
            val payload = intent.extras?.parcelable<GapoNotificationManager.Payload>(
                NOTIFICATION_PAYLOAD_EXTRA
            )
            if (payload != null) {
                onClickNotificationInAppBusEvent(OnClickNotificationInAppBusEvent(payload))
            }
        }
    }

    private inline fun <reified T : Fragment> setUnreadCountBadge(count: Int) {
        val position = fragments.indexOfFirst { it is T }
        if (position != -1) {
            if (count > 0) {
                val badgeDrawable = binding.bottomTab.getTabAt(position)?.orCreateBadge ?: return
                if (badgeDrawable.number == count) return
                badgeDrawable.number = count
                badgeDrawable.maxCharacterCount = 3
                badgeDrawable.backgroundColor = ContextCompat.getColor(this, GapoColors.red)
            } else {
                binding.bottomTab.getTabAt(position)?.removeBadge()
            }
        }
    }

    private fun switchFeedFollowTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            FEED_FOLLOW_POSITION,
            smoothScroll
        )
    }

    private fun switchFeedGroupsTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            FEED_GROUPS_POSITION,
            smoothScroll
        )
    }

    private fun switchMessengerTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            CONVERSATION_POSITION,
            smoothScroll
        )
    }

    private fun switchNotificationTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            NOTIFICATION_POSITION,
            smoothScroll
        )
    }

    private fun switchCalendarTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            CALENDAR_POSITION,
            smoothScroll
        )
    }

    private fun switchMenuTab(smoothScroll: Boolean = false) {
        binding.mainViewPager.setCurrentItem(
            HomePagerAdapter.MENU_POSITION,
            smoothScroll
        )
    }

    private fun trackingHomeChildScreen(currentItem: Int) {
        when (currentItem) {
            FEED_FOLLOW_POSITION -> {
                GAPOAnalytics.getInstance(this@HomeActivity)
                    .logScreenHomePageFollowing(this@HomeActivity)
            }

            FEED_GROUPS_POSITION -> {
                GAPOAnalytics.getInstance(this@HomeActivity).logScreenGroup(this@HomeActivity)
            }

            CONVERSATION_POSITION -> {
                GAPOAnalytics.getInstance(this@HomeActivity).logScreenChat(this@HomeActivity)
            }

            NOTIFICATION_POSITION -> {
                GAPOAnalytics.getInstance(this@HomeActivity)
                    .logScreenNotificationList(this@HomeActivity)
            }
        }
    }

    private fun Intent.hasAndTrueExtra(key: String) = hasExtra(key) && getBooleanExtra(key, false)

    private fun navToListWorkspace() {
        navByDeepLink(
            WorkspaceInvitationDeepLink(
                GapoDeepLink.Options(
                    bundleOf(
                        WorkspaceInvitationDeepLink.TYPE_EXTRA to WorkspaceInvitationDeepLink.TYPE_NORMAL_PREVENT_BACK_EXTRA
                    )
                )
            )
        )
    }

    private fun askNotificationPermission() {
        // This is only necessary for API level >= 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                // Do nothing
            } else if (shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                // Vì hiện tại targetSdk = 32 nên cần check để openAppSystemNotificationSettings
                val aboveTiramisu =
                    applicationContext.applicationInfo.targetSdkVersion >= Build.VERSION_CODES.TIRAMISU
                val positiveText = if (aboveTiramisu) {
                    GapoStrings.permission_agree
                } else {
                    GapoStrings.permission_app_settings
                }
                showNotificationPermissionDialog(positiveText, GapoStrings.permission_later) {
                    if (aboveTiramisu) {
                        postNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                    } else {
                        openAppSystemNotificationSettings()
                    }
                }
            } else {
                postNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        } else if (!NotificationManagerCompat.from(this).areNotificationsEnabled()) {
            showNotificationPermissionDialog(
                GapoStrings.permission_app_settings,
                GapoStrings.permission_later
            ) {
                openAppSystemNotificationSettings()
            }
        }
    }

    private fun showNotificationPermissionDialog(
        @StringRes positive: Int,
        @StringRes negative: Int,
        onPositive: () -> Unit
    ) {
        AlertDialog.Builder(this, GapoStyles.GapoDialog_Alert)
            .setTitle(
                getString(GapoStrings.notification_ask_enable_dialog_title).replaceByBrandingName(
                    homeViewModel.brandName
                )
            )
            .setPositiveButton(positive) { dialog, _ ->
                dialog.dismiss()
                onPositive()
            }.setNegativeButton(negative) { dialog, _ -> dialog.dismiss() }.setCancelable(false)
            .setMessage(GapoStrings.notification_ask_enable_dialog_message).show()
    }

    private fun checkChangeLauncherApp() {
        kotlin.runCatching {
            val appIcon = homeViewModel.workspaceManager.currentWorkspace?.appIcon
            if (appIcon != null) {
                setComponentEnabledSetting(WorkspaceIconLauncher.findByName(appIcon))
            }
        }
    }

    private fun showDialogRemindYody() {
        val alertDialog = YodyRecordDialogFragment.newInstance()
        alertDialog.show(
            supportFragmentManager,
            YodyRecordDialogFragment::class.java.simpleName
        )
    }

    private fun handleUrgentlyApprove(
        data: DeeplinkHandleApproveEventBus
    ) {
        if (!homeViewModel.workspaceManager.workspaces.map { it.id }
            .contains(data.workspaceId)
        ) {
            GapoToast.makeNegative(
                this,
                GapoStrings.new_approval_not_joined_workspace_popup_desc
            )
                .showOnTop()
            return
        }

        if (data.workspaceId != homeViewModel.currentWorkspaceId) {
            homeViewModel.approveRequestHolder = DeeplinkHandleApproveEventBus(
                requestId = data.requestId,
                tabId = data.tabId,
                workflowId = data.workflowId,
                status = data.status,
                workspaceId = data.workspaceId,
                isAdvanceWorkflow = data.isAdvanceWorkflow
            )
            homeViewModel.workspaceManager.switchWorkspace(data.workspaceId)
            return
        }

        homeViewModel.handleTaskRequestState(
            id = data.requestId,
            stateId = data.workflowId,
            status = data.status,
            flowType = data.isAdvanceWorkflow,
            workspaceId = data.workspaceId
        )
    }

    companion object {
        const val NOTIFICATION_PAYLOAD_EXTRA = "NOTIFICATION_PAYLOAD_EXTRA"
    }
}
