{"formatVersion": "1.1", "component": {"group": "com.spencerccf.app_settings", "module": "app_settings_release", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "app_settings_release-1.0.aar", "url": "app_settings_release-1.0.aar", "size": 8908, "sha512": "22a020e2b61f55ae41daee0c15d1e5c7a6775cc8a9072afe0d1f36d7e5b208e4235d1c28f206986b73cc1b26c7041b407489e19c125f5d323dd111c3ac2c6a8a", "sha256": "b100a9b5a049e5818b6c12505819386ffa352c99c07513cd72c3c409b8246713", "sha1": "df37a29be4ba0c82b73f08e19d8f740c5e47abbf", "md5": "659d999d80f74695df440678b1a2f759"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.0"}}], "files": [{"name": "app_settings_release-1.0.aar", "url": "app_settings_release-1.0.aar", "size": 8908, "sha512": "22a020e2b61f55ae41daee0c15d1e5c7a6775cc8a9072afe0d1f36d7e5b208e4235d1c28f206986b73cc1b26c7041b407489e19c125f5d323dd111c3ac2c6a8a", "sha256": "b100a9b5a049e5818b6c12505819386ffa352c99c07513cd72c3c409b8246713", "sha1": "df37a29be4ba0c82b73f08e19d8f740c5e47abbf", "md5": "659d999d80f74695df440678b1a2f759"}]}]}