{"formatVersion": "1.1", "component": {"group": "com.baseflow.permissionhandler", "module": "permission_handler_android_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "permission_handler_android_debug-1.0.aar", "url": "permission_handler_android_debug-1.0.aar", "size": 30042, "sha512": "e221f42bdc621e746be49309bfb463bd9a542e2b225956298ce8a7ac2db317cd909fa3cc7a8eddfad851669823fbaa8fff3519c60bda455664ffa5d15a334b43", "sha256": "3a5fe1682654c41f71311aecc8ad1734613f9e06574ea2b1d38a605561e0dd8a", "sha1": "07470eab967ee81ece4e05ef741696eee3fbafb3", "md5": "8481b8719df6833198f332533f14e4e9"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "permission_handler_android_debug-1.0.aar", "url": "permission_handler_android_debug-1.0.aar", "size": 30042, "sha512": "e221f42bdc621e746be49309bfb463bd9a542e2b225956298ce8a7ac2db317cd909fa3cc7a8eddfad851669823fbaa8fff3519c60bda455664ffa5d15a334b43", "sha256": "3a5fe1682654c41f71311aecc8ad1734613f9e06574ea2b1d38a605561e0dd8a", "sha1": "07470eab967ee81ece4e05ef741696eee3fbafb3", "md5": "8481b8719df6833198f332533f14e4e9"}]}]}