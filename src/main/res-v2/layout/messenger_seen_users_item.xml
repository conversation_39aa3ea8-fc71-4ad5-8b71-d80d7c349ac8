<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/layout_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:paddingTop="@dimen/_10dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingEnd="@dimen/_10dp"
        android:clipChildren="false"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.gg.gapo.core.ui.image.GapoAvatarImageView
            android:id="@+id/image_avatar"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:adjustViewBounds="true"
            android:clickable="false"
            android:focusable="false"
            tools:background="@android:color/darker_gray" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_name"
            style="@style/GapoTextStyle.HeadingSmall"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_weight="1"
            android:clickable="false"
            android:ellipsize="end"
            android:focusable="false"
            android:lines="1"
            android:singleLine="true"
            android:textColor="@color/contentPrimary"
            tools:text="Display Name" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>