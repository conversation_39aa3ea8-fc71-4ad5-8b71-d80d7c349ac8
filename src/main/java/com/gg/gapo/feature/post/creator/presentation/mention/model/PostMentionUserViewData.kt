package com.gg.gapo.feature.post.creator.presentation.mention.model

import android.text.Spanned
import android.text.SpannedString
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.DiffUtil
import com.gg.gapo.core.feed.item.post.comment.model.FeedPostCommentContentViewData
import com.gg.gapo.core.feed.post.domain.PostUserModel

/**
 * <AUTHOR>
 * @since 04/08/2021
 */
internal data class PostMentionUserViewData(
    val id: String,
    val name: String,
    val avatar: String,
    val nameSpanned: Spanned
) {

    fun shallowCopy(): PostMentionUserViewData = copy(nameSpanned = nameSpanned)

    object DiffCallback : DiffUtil.ItemCallback<PostMentionUserViewData>() {
        override fun areItemsTheSame(
            oldItem: PostMentionUserViewData,
            newItem: PostMentionUserViewData
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: PostMentionUserViewData,
            newItem: PostMentionUserViewData
        ): Boolean {
            return oldItem.name == newItem.name && oldItem.avatar == newItem.avatar
        }
    }

    class LiveData : MutableLiveData<List<PostMentionUserViewData>>() {
        override fun setValue(value: List<PostMentionUserViewData>) {
            super.setValue(value.mapAndCopy())
        }

        override fun postValue(value: List<PostMentionUserViewData>) {
            super.postValue(value.mapAndCopy())
        }

        private fun List<PostMentionUserViewData>.mapAndCopy() = map { it.shallowCopy() }
    }

    interface OnClickPostMentionUserListener {
        fun onClickPostMentionUserItem(item: PostMentionUserViewData)
    }
}

internal fun PostUserModel.mapToMentionUserViewData() =
    PostMentionUserViewData(
        id,
        displayName,
        FeedPostCommentContentViewData.parseAvatarMedium(avatar, avatarThumbPattern).orEmpty(),
        SpannedString(displayName)
    )

internal fun List<PostUserModel>.mapToMentionUserViewData() =
    map { it.mapToMentionUserViewData() }
