package com.gg.gapo.feature.post.presentation.comment.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.ObjectType
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentCreatedOrUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadedBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadingBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.feed.common.FeedItemsMutableList
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.item.post.comment.model.FeedPostCommentViewData
import com.gg.gapo.core.feed.mapper.FeedCommentMapper
import com.gg.gapo.core.feed.mapper.FeedCommentMapperImpl
import com.gg.gapo.core.feed.post.domain.PostCommentModel
import com.gg.gapo.core.feed.post.domain.PostModel
import com.gg.gapo.core.feed.spanner.FeedCommentTextSpanner
import com.gg.gapo.core.feed.spanner.FeedTextContentSpanner
import com.gg.gapo.core.feed.usecase.comment.domain.usecase.CreateOrUpdateCommentUseCase
import com.gg.gapo.core.feed.usecase.post.domain.usecase.FeedPostUseCaseFacade
import com.gg.gapo.core.feed.usecase.post.domain.usecase.FetchPostPreviewLinkUseCase
import com.gg.gapo.core.feed.usecase.search.domain.usecase.SearchUsersUseCase
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.feature.post.presentation.comment.input.viewmodel.CommentInputViewModel
import com.gg.gapo.feature.post.presentation.utils.clipTextContentToShare
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 23/06/2021
 */
internal class CommentsViewModel(
    private val application: Application,
    userManager: UserManager,
    private val workspaceManager: WorkspaceManager,
    private val postUseCaseFacade: FeedPostUseCaseFacade,
    createOrUpdateCommentUseCase: CreateOrUpdateCommentUseCase,
    searchUsersUseCase: SearchUsersUseCase,
    fetchPostPreviewLinkUseCase: FetchPostPreviewLinkUseCase,
    private val appDispatchers: CoroutineDispatchers,
    private val params: Params
) : CommentInputViewModel(
    application,
    userManager,
    createOrUpdateCommentUseCase,
    searchUsersUseCase,
    fetchPostPreviewLinkUseCase,
    params.postId
),
    FeedCommentMapper.Options {

    data class Params(
        val postId: String,
        val commentId: String? = null,
        val parentCommentId: String? = null,
        val mediaId: String? = null,
        var isAutoShowKeyboardWhenNav: Boolean = false
    )

    private val postId: String
        get() = params.postId

    override val parentCommentId: String?
        get() = params.parentCommentId

    override val mediaId: String?
        get() = params.mediaId

    override val commentContext: FeedCommentMapper.CommentContext
        get() = if (!mediaId.isNullOrEmpty()) {
            FeedCommentMapper.CommentContext.MediaComment
        } else if (parentCommentId.isNullOrEmpty()) {
            FeedCommentMapper.CommentContext.PostDetails
        } else {
            FeedCommentMapper.CommentContext.ReplyComment(target)
        }

    private val contentTextSpanner = FeedTextContentSpanner(application, this)
    private val commentTextSpanner = FeedCommentTextSpanner(application)

    private val commentMapper: FeedCommentMapper = FeedCommentMapperImpl(
        application,
        contentTextSpanner,
        commentTextSpanner,
        this,
        this
    )

    val isFeatureDownloadEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.DOWNLOAD) ?: true

    val onScrollToCommentPositionLiveData: LiveData<Event<Int>>
        get() = _onScrollToCommentPositionLiveData
    private val _onScrollToCommentPositionLiveData = MutableLiveData<Event<Int>>()

    val feedViewDataLiveData: LiveData<List<FeedViewData>>
        get() = feedItems.liveData

    val isCommentsFetching: Boolean
        get() = nextCommentsQueries.isNotEmpty() && fetchCommentsJob?.isActive == true

    private val target: PostCommentModel.Target
        get() {
            val mediaId = mediaId
            return if (mediaId.isNullOrEmpty()) {
                PostCommentModel.Target(postId, PostCommentModel.Target.Type.POST)
            } else {
                PostCommentModel.Target(mediaId, PostCommentModel.Target.Type.MEDIA)
            }
        }

    private val defaultCommentsQueries: Map<String, String>
        get() {
            return if (postId.isEmpty()) emptyMap()
            else mutableMapOf(
                "target_id" to target.id,
                "limit" to "10",
                "sort" to "create_time:asc",
                "target_type" to target.type.type,
                "version" to "5"
            )
                .apply {
                    val highlightCommentId = params.commentId
                    if (!highlightCommentId.isNullOrEmpty()) {
                        this["highlight"] = highlightCommentId
                    }
                }
        }

    private val isViewParentComment: Boolean
        get() = !params.commentId.isNullOrEmpty() || params.parentCommentId.isNullOrEmpty()

    private val defaultReplyCommentsQueries: Map<String, String>
        get() {
            val parentCommentId = parentCommentId
            return if (postId.isEmpty() || parentCommentId.isNullOrEmpty()) emptyMap()
            else {
                mutableMapOf(
                    "target_id" to target.id,
                    "limit" to "10",
                    "target_type" to target.type.type,
                    "version" to "5"
                ).apply {
                    val highlightCommentId = params.commentId
                    if (!highlightCommentId.isNullOrEmpty()) {
                        this["highlight"] = highlightCommentId
                    } else {
                        this["parent_id"] = params.parentCommentId.toString()
                    }
                }
            }
        }

    private val indexOfHighlightCommentId: Int
        get() {
            val highlightCommentId = params.commentId
            return if (!highlightCommentId.isNullOrEmpty() && postId.isNotEmpty()) {
                feedItems.indexOfFirst {
                    it.isFeedPostCommentViewData(
                        postId,
                        highlightCommentId,
                        params.parentCommentId
                    )
                }
            } else {
                FeedItemsMutableList.NO_POSITION
            }
        }

    private val nextCommentsQueries = mutableMapOf<String, String>()

    private val previousCommentsQueries = mutableMapOf<String, String>()

    private var fetchCommentsJob: Job? = null

    private var isScrolledToHighlightComment = false

    val onFetchPostCommentSucceededEventLiveData: LiveData<Event<Pair<PostModel, PostCommentModel>>>
        get() = _onFetchPostCommentSucceededEventLiveData
    private val _onFetchPostCommentSucceededEventLiveData =
        MutableLiveData<Event<Pair<PostModel, PostCommentModel>>>()

    val onFetchPostCommentErrorEventLiveData: LiveData<Event<Unit>>
        get() = _onFetchPostCommentErrorEventLiveData
    private val _onFetchPostCommentErrorEventLiveData =
        MutableLiveData<Event<Unit>>()

    val invalidateItemDecorationsLiveData: LiveData<Event<Unit>>
        get() = _invalidateItemDecorationsLiveData
    private val _invalidateItemDecorationsLiveData = MutableLiveData<Event<Unit>>()

    private val _isDisplayCommentEmptyLiveData = MutableLiveData<Boolean>()
    val isDisplayCommentEmptyLiveData: LiveData<Boolean> get() = _isDisplayCommentEmptyLiveData

    val onFinishEventLiveData: LiveData<Event<Unit>>
        get() = _onFinishEventLiveData
    private val _onFinishEventLiveData = MutableLiveData<Event<Unit>>()

    var needScrollToTop = false

    private val mapCommentsMutex = Mutex()

    init {
        registerEventBus()
    }

    override fun onCleared() {
        super.onCleared()
        unregisterEventBus()
    }

    override fun onPostCommentExpandTextContent(
        itemId: String,
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        feedItems.expandCommentText(itemId)
    }

    override fun onPostCommentClickOnReact(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        reactType: Int,
        target: PostCommentModel.Target
    ) {
        viewModelScope.launch {
            feedItems.onPostCommentClickOnReact(
                postUseCaseFacade.reactCommentUseCase,
                postId,
                commentId,
                parentCommentId,
                reactType,
                target
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostDeleted(event: FeedPostDeletedBusEvent) {
        if (postId == event.postId) {
            _onFinishEventLiveData.value = Event(Unit)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onCreatedOrUpdatedComment(event: FeedCommentCreatedOrUpdatedBusEvent) {
        val commentModel = event.commentModel as? PostCommentModel ?: return
        // handler created or update comment reply comment screen isn't view parent comment
        val commentCreatedOrUpdate =
            if (isViewParentComment) commentModel else commentModel.copy(parentId = null)
        val post = feedItems.addOrSetComment(
            FeedCommentCreatedOrUpdatedBusEvent(
                commentCreatedOrUpdate,
                event.target,
                event.changeCommentCount
            )
        )
        mapComments(post)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onDeletedComment(event: FeedCommentDeletedBusEvent) {
        val post = feedItems.removeComment(event)
        mapComments(post)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostCommentAttachmentDownloading(event: FeedPostCommentAttachmentDownloadingBusEvent) {
        feedItems.handlePostCommentAttachmentDownloadingEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostCommentAttachmentDownloaded(event: FeedPostCommentAttachmentDownloadedBusEvent) {
        feedItems.handlePostCommentAttachmentDownloadedEvent(event)
    }

    fun fetchData() {
        val parentCommentId = parentCommentId
        if (parentCommentId.isNullOrEmpty()) {
            fetchComments(postId)
        } else {
            fetchReplyComments(postId, parentCommentId)
        }
    }

    fun invalidateItemDecorations() {
        _invalidateItemDecorationsLiveData.value = Event(Unit)
    }

    fun deleteComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        viewModelScope.launch {
            val comment = feedItems.getComment(postId, commentId, parentCommentId, target)
            when (val result = postUseCaseFacade.deleteCommentByIdUseCase(commentId)) {
                is Result.Success -> {
                    FeedCommentDeletedBusEvent(
                        postId,
                        commentId,
                        parentCommentId,
                        target,
                        comment?.replyCount ?: 0L
                    ).postEvent()
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun fetchComments(postId: String) {
        fetchCommentsJob = viewModelScope.launch {
            val postModelDeferred = viewModelScope.async {
                postUseCaseFacade.fetchPostByIdUseCase(postId)
            }
            val commentModelDeferred = viewModelScope.async {
                postUseCaseFacade.fetchCommentsUseCase(defaultCommentsQueries)
            }
            val postModelResult = postModelDeferred.await()
            val commentModelResult = commentModelDeferred.await()

            if (postModelResult is Result.Success && commentModelResult is Result.Success) {
                val postModel = postModelResult.data
                groupId = postModel.group?.id
                FeedPostUpdatedBusEvent(postModel.shallowCopy()).postEvent()

                // không quan tâm comments ở đây.
                feedItems.setAllPosts(
                    listOf(postModel.shallowCopy().also { it.comments.clear() }),
                    emptyList()
                )

                val commentModels = commentModelResult.data.first
                val previousQueries = commentModelResult.data.second
                val nextQueries = commentModelResult.data.third
                previousCommentsQueries.clear()
                previousCommentsQueries.putAll(previousQueries)
                nextCommentsQueries.clear()
                nextCommentsQueries.putAll(nextQueries)

                if (previousQueries.isNotEmpty()) {
                    commentModels.map { it.hasPreviousComment = true }
                }
                showAutoKeyboardIfNeed()
                val postHasNewComment =
                    feedItems.addAllComments(postId, commentModels, target)
                mapComments(postHasNewComment)
            } else {
                if (postModelResult is Result.Error) {
                    toastErrorMessage(postModelResult.exception)
                }

                if (commentModelResult is Result.Error) {
                    toastErrorMessage(commentModelResult.exception)
                }
            }
        }
    }

    private fun fetchReplyComments(postId: String, parentCommentId: String) {
        fetchCommentsJob = viewModelScope.launch {
            val postModelDeferred = viewModelScope.async {
                postUseCaseFacade.fetchPostByIdUseCase(postId)
            }
            val commentModelDeferred =
                viewModelScope.async {
                    postUseCaseFacade.fetchCommentsUseCase(
                        defaultReplyCommentsQueries
                    )
                }

            val postModelResult = postModelDeferred.await()
            val commentModelResult = commentModelDeferred.await()

            if (postModelResult is Result.Success && commentModelResult is Result.Success) {
                val postModel = postModelResult.data
                FeedPostUpdatedBusEvent(postModel.shallowCopy()).postEvent()

                // không quan tâm comments ở đây.
                feedItems.setAllPosts(
                    listOf(postModel.shallowCopy().also { it.comments.clear() }),
                    emptyList()
                )

                if (params.commentId.isNullOrEmpty()) {
                    // chỉ hiển thị list reply comment, không hiện comment cha
                    // do không hiện comment cha nên map parentId = null
                    val comments = commentModelResult.data.first.map {
                        it.copy(parentId = null)
                    }
                    previousCommentsQueries.clear()
                    previousCommentsQueries.putAll(commentModelResult.data.second)
                    nextCommentsQueries.clear()
                    nextCommentsQueries.putAll(commentModelResult.data.third)
                    val postHasNewComment =
                        feedItems.addAllComments(
                            postId,
                            comments,
                            target
                        )
                    mapComments(postHasNewComment)
                } else {
                    // hiện comment cha và các comments reply
                    val parentComment = commentModelResult.data.first.firstOrNull()
                    if (parentComment != null) {
                        val postHasNewComment =
                            feedItems.addAllComments(
                                postId,
                                listOf(parentComment),
                                target
                            )
                        mapComments(postHasNewComment)

                        if (postHasNewComment != null) {
                            _onFetchPostCommentSucceededEventLiveData.value =
                                Event(postModelResult.data to parentComment)

                            val replyComments = parentComment.comments
                            previousCommentsQueries.clear()
                            previousCommentsQueries.putAll(parentComment.previousQueries)
                            nextCommentsQueries.clear()
                            nextCommentsQueries.putAll(parentComment.nextQueries)

                            if (parentComment.previousQueries.isNotEmpty()) {
                                replyComments.map {
                                    it.hasPreviousComment = true
                                }
                            }
                            val postHasNewReplyComment =
                                feedItems.addAllComments(postId, replyComments, target)
                            mapComments(postHasNewReplyComment)
                        }
                    }
                }
            } else {
                _onFetchPostCommentErrorEventLiveData.value = Event(Unit)

                if (postModelResult is Result.Error) {
                    toastErrorMessage(postModelResult.exception)
                }

                if (commentModelResult is Result.Error) {
                    toastErrorMessage(commentModelResult.exception)
                }
            }
        }
    }

    override fun onPostCommentClickViewMorePreviousComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        viewModelScope.launch {
            fetchPreviousComments()
        }
    }

    fun fetchMore() {
        viewModelScope.launch {
            fetchMoreComments()
        }
    }

    fun scrollToHighlightComment() {
        if (indexOfHighlightCommentId != FeedItemsMutableList.NO_POSITION && !isScrolledToHighlightComment) {
            _onScrollToCommentPositionLiveData.value = Event(indexOfHighlightCommentId)
            isScrolledToHighlightComment = true
        }
    }

    private suspend fun fetchMoreComments() {
        if (nextCommentsQueries.isEmpty()) return
        when (val result = postUseCaseFacade.fetchCommentsUseCase(nextCommentsQueries)) {
            is Result.Success -> {
                val commentModels = result.data.first
                val nextQueries = result.data.third

                nextCommentsQueries.clear()
                nextCommentsQueries.putAll(nextQueries)

                if (commentModels.isEmpty()) return
                val comments = if (params.commentId.isNullOrEmpty()) commentModels.map {
                    it.copy(parentId = null)
                } else commentModels
                val postHasNewComment = feedItems.addAllComments(postId, comments, target)
                mapComments(postHasNewComment)
            }

            is Result.Error -> {
                Timber.e(result.exception)
            }
        }
    }

    private suspend fun fetchPreviousComments() {
        when (val result = postUseCaseFacade.fetchCommentsUseCase(previousCommentsQueries)) {
            is Result.Success -> {
                val commentModels = result.data.first
                val previousQueries = result.data.second

                previousCommentsQueries.clear()
                previousCommentsQueries.putAll(previousQueries)

                if (commentModels.isEmpty()) return

                val postHasNewComment =
                    feedItems.addAllPreviousComments(postId, commentModels, target)
                if (previousQueries.isNotEmpty()) {
                    commentModels.map {
                        it.hasPreviousComment = true
                    }
                }
                mapComments(postHasNewComment)
                if (parentCommentId.isNullOrEmpty()) {
                    needScrollToTop = true
                }
            }

            is Result.Error -> {
                Timber.e(result.exception)
            }
        }
    }

    private fun mapComments(post: PostModel?) {
        viewModelScope.launch {
            if (post != null) {
                mapCommentsMutex.withLock {
                    val copiedPost = post.shallowCopy()
                    val comments = withContext(appDispatchers.default) {
                        if (mediaId.isNullOrEmpty()) {
                            copiedPost.comments
                        } else {
                            copiedPost.media.find { it.mediaId == mediaId }?.comments.orEmpty()
                        }.map {
                            val comments = mutableListOf<FeedPostCommentViewData>()
                            comments.add(commentMapper.map(it, params.commentId))
                            // add reply comment
                            if (parentCommentId.isNullOrEmpty()) {
                                if (it.comments.isNotEmpty()) {
                                    comments.add(
                                        commentMapper.mapShortReplies(
                                            it,
                                            params.commentId
                                        )
                                    )
                                }
                            } else {
                                comments.addAll(
                                    it.comments.map { reply ->
                                        commentMapper.map(
                                            reply,
                                            params.commentId
                                        )
                                    }
                                )
                            }
                            comments
                        }
                    }
                    _isDisplayCommentEmptyLiveData.value = comments.isEmpty()
                    if (feedItems.setAllPosts(listOf(copiedPost), comments)) {
                        feedItems.setLiveData()
                    }
                }
            } else {
                _isDisplayCommentEmptyLiveData.value = true
            }
        }
    }

    fun switchReplyCommentMode(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        switchReplyCommentMode(commentModel, parentCommentId ?: commentId)
    }

    fun switchEditCommentMode(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        switchEditCommentMode(commentModel, parentCommentId)
    }

    fun getPost(postId: String) = feedItems.getPost(postId)

    fun getComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) = feedItems.getComment(postId, commentId, parentCommentId, target)

    fun copyCommentContent(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        application.clipTextContentToShare(commentModel.text)

        GAPOAnalytics.getInstance(application)
            .logEventCopyContent(
                postId,
                ObjectType.COMMENT,
                screenName = GAPOAnalyticsEvents.SCREEN_COMMENT
            )
    }

    private fun showAutoKeyboardIfNeed() {
        viewModelScope.launch {
            delay(50)
            if (params.isAutoShowKeyboardWhenNav) {
                openKeyboard()
                params.isAutoShowKeyboardWhenNav = false
            }
        }
    }

    private fun toastErrorMessage(exception: Throwable) {
        Timber.e(exception)
        val errorMessage = exception.message
        if (!errorMessage.isNullOrEmpty()) {
            GapoToast.makeNegative(application, errorMessage).show()
        }
    }
}
