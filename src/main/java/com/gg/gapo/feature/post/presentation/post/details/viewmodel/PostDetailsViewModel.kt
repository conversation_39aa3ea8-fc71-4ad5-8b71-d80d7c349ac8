package com.gg.gapo.feature.post.presentation.post.details.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.ObjectType
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentCreatedOrUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostMediaDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadCanceledBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadedBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadingBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadedBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadingBusEvent
import com.gg.gapo.core.eventbus.feed.post.pollvote.FeedPostPollExpiredBusEvent
import com.gg.gapo.core.eventbus.feed.post.pollvote.FeedPostPollVoteCreatedBusEvent
import com.gg.gapo.core.eventbus.livestream.LiveStreamFinishedBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.feed.common.FeedItemsMutableList
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.item.shimmer.model.FeedPostShimmerViewData
import com.gg.gapo.core.feed.mapper.FeedCommentMapper
import com.gg.gapo.core.feed.mapper.FeedCommentMapperImpl
import com.gg.gapo.core.feed.mapper.FeedPostMapper
import com.gg.gapo.core.feed.mapper.FeedPostMapperImpl
import com.gg.gapo.core.feed.post.domain.PostCommentModel
import com.gg.gapo.core.feed.post.domain.PostGroupModel
import com.gg.gapo.core.feed.post.domain.PostModel
import com.gg.gapo.core.feed.post.domain.PostUserModel
import com.gg.gapo.core.feed.spanner.FeedCommentTextSpanner
import com.gg.gapo.core.feed.spanner.FeedPostAchievementTextSpanner
import com.gg.gapo.core.feed.spanner.FeedPostHeaderTextSpanner
import com.gg.gapo.core.feed.spanner.FeedTextContentSpanner
import com.gg.gapo.core.feed.usecase.comment.domain.usecase.CreateOrUpdateCommentUseCase
import com.gg.gapo.core.feed.usecase.post.domain.usecase.FeedPostUseCaseFacade
import com.gg.gapo.core.feed.usecase.post.domain.usecase.FetchPostPreviewLinkUseCase
import com.gg.gapo.core.feed.usecase.search.domain.usecase.SearchUsersUseCase
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.feature.post.presentation.comment.input.viewmodel.CommentInputViewModel
import com.gg.gapo.feature.post.presentation.utils.clipTextContentToShare
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 15/06/2021
 */
internal class PostDetailsViewModel(
    private val application: Application,
    createOrUpdateCommentUseCase: CreateOrUpdateCommentUseCase,
    searchUsersUseCase: SearchUsersUseCase,
    fetchPostPreviewLinkUseCase: FetchPostPreviewLinkUseCase,
    private val appDispatchers: CoroutineDispatchers,
    private val postUseCaseFacade: FeedPostUseCaseFacade,
    private val userManager: UserManager,
    private val workspaceManager: WorkspaceManager,
    private val params: Params
) : CommentInputViewModel(
    application,
    userManager,
    createOrUpdateCommentUseCase,
    searchUsersUseCase,
    fetchPostPreviewLinkUseCase,
    params.postId
),
    FeedPostMapper.Options,
    FeedCommentMapper.Options {

    data class Params(
        val postId: String,
        var highlightCommentId: String? = null,
        var isAutoShowKeyboardWhenNav: Boolean = false
    )

    val postId: String
        get() = params.postId

    override val myUserProfile: PostUserModel
        get() {
            val user = userManager.userProfile
            return PostUserModel.create(
                user?.id.orEmpty(),
                user?.displayName.orEmpty(),
                user?.avatar.orEmpty(),
                user?.avatarThumbPattern.orEmpty()
            )
        }

    override val postContext: FeedPostMapper.PostContext
        get() = FeedPostMapper.PostContext.PostDetails

    override val commentContext: FeedCommentMapper.CommentContext
        get() = FeedCommentMapper.CommentContext.PostDetails

    private val postHeaderTextSpanner = FeedPostHeaderTextSpanner(application, this)
    private val contentTextSpanner = FeedTextContentSpanner(application, this)
    private val commentTextSpanner = FeedCommentTextSpanner(application)
    private val postAchievementTextSpanner = FeedPostAchievementTextSpanner(application)

    val isFeatureDownloadEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.DOWNLOAD) ?: true

    private val postMapper: FeedPostMapper = FeedPostMapperImpl(
        application,
        postHeaderTextSpanner,
        contentTextSpanner,
        postAchievementTextSpanner,
        this,
        this,
        isFeatureMessengerEnabled = workspaceManager.currentWorkspace?.features?.isEnable(Feature.MESSENGER) ?: true
    )

    private val commentMapper: FeedCommentMapper = FeedCommentMapperImpl(
        application,
        contentTextSpanner,
        commentTextSpanner,
        this,
        this
    )

    private val defaultCommentsQueries: Map<String, String>
        get() {
            return if (postId.isEmpty()) emptyMap()
            else mutableMapOf(
                "target_id" to postId,
                "limit" to "10",
                "sort" to "create_time:asc",
                "target_type" to "post",
                "version" to "5"
            ).apply {
                val highlightCommentId = params.highlightCommentId
                if (!highlightCommentId.isNullOrEmpty()) {
                    this["highlight"] = highlightCommentId
                }
            }
        }

    private val nextCommentsQueries = mutableMapOf<String, String>()

    private val previousCommentQueries = mutableMapOf<String, String>()

    val isCommentsFetching: Boolean
        get() = nextCommentsQueries.isNotEmpty() && fetchCommentsJob?.isActive == true

    private var fetchCommentsJob: Job? = null

    val feedViewDataLiveData: LiveData<List<FeedViewData>>
        get() = feedItems.liveData

    private val indexOfHighlightCommentId: Int
        get() {
            val highlightCommentId = params.highlightCommentId
            return if (!highlightCommentId.isNullOrEmpty() && postId.isNotEmpty()) {
                feedItems.indexOfFirst {
                    it.isFeedPostCommentViewData(
                        postId,
                        highlightCommentId,
                        null
                    )
                }
            } else {
                FeedItemsMutableList.NO_POSITION
            }
        }

    val isSwipeRefreshLayoutVisibleLiveData: LiveData<Boolean>
        get() = _isSwipeRefreshLayoutVisibleLiveData
    private val _isSwipeRefreshLayoutVisibleLiveData = MutableLiveData<Boolean>(false)

    val isNotificationVisibleLiveData: LiveData<Boolean>
        get() = _isNotificationVisibleLiveData
    private val _isNotificationVisibleLiveData = MutableLiveData(false)

    val postErrorLiveData: LiveData<Throwable>
        get() = _postErrorLiveData
    private val _postErrorLiveData = MutableLiveData<Throwable>()

    val onClickButtonBackLiveData: LiveData<Event<Unit>>
        get() = _onClickButtonBackLiveData
    private val _onClickButtonBackLiveData = MutableLiveData<Event<Unit>>()

    val onClickButtonErrorNavLiveData: LiveData<Event<Throwable>>
        get() = _onClickButtonErrorNavLiveData
    private val _onClickButtonErrorNavLiveData = MutableLiveData<Event<Throwable>>()

    val onDeletePostSucceededEventLiveData: LiveData<Event<Unit>>
        get() = _onDeletePostSucceededEventLiveData
    private val _onDeletePostSucceededEventLiveData = MutableLiveData<Event<Unit>>()

    val invalidateItemDecorationsLiveData: LiveData<Event<Unit>>
        get() = _invalidateItemDecorationsLiveData
    private val _invalidateItemDecorationsLiveData = MutableLiveData<Event<Unit>>()

    val onScrollToCommentPositionLiveData: LiveData<Event<Int>>
        get() = _onScrollToCommentPositionLiveData
    private val _onScrollToCommentPositionLiveData = MutableLiveData<Event<Int>>()

    private var fetchPostJob: Job? = null

    private val mapPostMutex = Mutex()

    private var isScrolledToHighlightComment = false

    init {
        registerEventBus()
        feedItems.add(FeedPostShimmerViewData(FeedPostShimmerViewData.DEFAULT_ITEM_ID, this))
        feedItems.setLiveData()
        fetchPost()
    }

    override fun onCleared() {
        super.onCleared()
        postHeaderTextSpanner.onCleared()
        unregisterEventBus()
    }

    override fun onPostPollVoteClickOnVote(postId: String, parentPostId: String?, voteId: String) {
        viewModelScope.launch {
            feedItems.onPostPollVoteClickOnVote(
                application,
                postUseCaseFacade.voteUseCase,
                myUserProfile,
                postId,
                voteId
            )
        }
    }

    override fun onPostPollVoteClickOnUnVote(
        postId: String,
        parentPostId: String?,
        voteId: String
    ) {
        viewModelScope.launch {
            feedItems.onPostPollVoteClickOnUnVote(
                application,
                postUseCaseFacade.unVoteUseCase,
                myUserProfile,
                postId,
                voteId
            )
        }
    }

    override fun onPostPollVoteClickOnViewMoreVote(
        itemId: String,
        postId: String,
        parentPostId: String?
    ) {
        feedItems.expandPollVote(itemId)
    }

    override fun onPostMediaVideoAddViewCount(
        postId: String,
        parentPostId: String?,
        mediaId: String
    ) {
        viewModelScope.launch {
            postUseCaseFacade.addVideoViewUseCase(postId, mediaId)
        }
    }

    override fun onPostReactClickOnReact(postId: String, mediaId: String?, reactType: Int) {
        viewModelScope.launch {
            feedItems.onPostReactClickOnReact(
                postUseCaseFacade.reactPostUseCase,
                postId,
                mediaId,
                reactType
            )
        }
    }

    override fun onPostCommentExpandTextContent(
        itemId: String,
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        feedItems.expandCommentText(itemId)
    }

    override fun onPostCommentClickOnReact(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        reactType: Int,
        target: PostCommentModel.Target
    ) {
        viewModelScope.launch {
            feedItems.onPostCommentClickOnReact(
                postUseCaseFacade.reactCommentUseCase,
                postId,
                commentId,
                parentCommentId,
                reactType,
                target
            )
        }
    }

    override fun onPostCommentClickViewMorePreviousComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        fetchPreviousComment()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostAttachmentDownloading(event: FeedPostAttachmentDownloadingBusEvent) {
        feedItems.handlePostAttachmentDownloadingEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostAttachmentDownloaded(event: FeedPostAttachmentDownloadedBusEvent) {
        feedItems.handlePostAttachmentDownloadedEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostAttachmentCancelDownload(event: FeedPostAttachmentDownloadCanceledBusEvent) {
        feedItems.handlePostAttachmentDownloadCanceledEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostUpdated(event: FeedPostUpdatedBusEvent) {
        viewModelScope.launch {
            mapPostMutex.withLock {
                val post = (event.postModel as? PostModel)?.shallowCopy() ?: return@withLock
                val currentPostModel = feedItems.getPost(post.id)?.shallowCopy()
                if (currentPostModel != null) {
                    post.comments.clear()
                    post.comments.addAll(currentPostModel.comments)
                    val items = withContext(appDispatchers.default) {
                        postMapper.mapPostDetails(post, commentMapper, params.highlightCommentId)
                    }
                    if (feedItems.setAllPosts(listOf(post), listOf(items))) {
                        feedItems.setLiveData()
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostDeleted(event: FeedPostDeletedBusEvent) {
        if (postId == event.postId) {
            _onDeletePostSucceededEventLiveData.value = Event(Unit)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostMediaDeleted(event: FeedPostMediaDeletedBusEvent) {
        val posts = feedItems.handlePostMediaDeletedEvent(event)
        posts.forEach { onPostUpdated(FeedPostUpdatedBusEvent(it)) }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onCommentCreatedOrUpdated(event: FeedCommentCreatedOrUpdatedBusEvent) {
        val newPost = feedItems.addOrSetComment(event, false)
        if (newPost != null) {
            onPostUpdated(FeedPostUpdatedBusEvent(newPost))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onCommentDeleted(event: FeedCommentDeletedBusEvent) {
        val newPost = feedItems.removeComment(event, false)
        if (newPost != null) {
            onPostUpdated(FeedPostUpdatedBusEvent(newPost))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostLiveStreamFinished(event: LiveStreamFinishedBusEvent) {
        val posts = feedItems.handlePostLiveStreamFinishedBusEvent(event.postId)
        posts.forEach { onPostUpdated(FeedPostUpdatedBusEvent(it)) }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostPollNewVoteCreated(event: FeedPostPollVoteCreatedBusEvent) {
        val posts = feedItems.handlePostPollNewVoteCreatedEvent(event)
        posts.forEach {
            onPostUpdated(FeedPostUpdatedBusEvent(it))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostPollExpired(event: FeedPostPollExpiredBusEvent) {
        val posts = feedItems.handlePostPollExpiredEvent(event)
        posts.forEach {
            onPostUpdated(FeedPostUpdatedBusEvent(it))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostCommentAttachmentDownloading(event: FeedPostCommentAttachmentDownloadingBusEvent) {
        feedItems.handlePostCommentAttachmentDownloadingEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onPostCommentAttachmentDownloaded(event: FeedPostCommentAttachmentDownloadedBusEvent) {
        feedItems.handlePostCommentAttachmentDownloadedEvent(event)
    }

    fun scrollToHighlightComment() {
        if (indexOfHighlightCommentId != FeedItemsMutableList.NO_POSITION && !isScrolledToHighlightComment) {
            _onScrollToCommentPositionLiveData.value = Event(indexOfHighlightCommentId)
            isScrolledToHighlightComment = true
        }
    }

    fun invalidateItemDecorations() {
        _invalidateItemDecorationsLiveData.value = Event(Unit)
    }

    fun onClickButtonBack() {
        _onClickButtonBackLiveData.value = Event(Unit)
    }

    fun onClickButtonErrorNav() {
        val exception = _postErrorLiveData.value ?: return
        _onClickButtonErrorNavLiveData.value = Event(exception)
    }

    fun onClickButtonOptions() {
        if (postId.isNotEmpty()) {
            onPostHeaderClickOnOptions(postId, null)
        }
    }

    fun onClickButtonNotification() {
        if (postId.isNotEmpty()) {
            onPostHeaderClickOnNotification(postId)
        }
    }

    fun fetchPost() {
        if (fetchPostJob?.isActive == true) return
        fetchPostJob = viewModelScope.launch {
            when (val result = postUseCaseFacade.fetchPostByIdUseCase(postId)) {
                is Result.Success -> {
                    _isSwipeRefreshLayoutVisibleLiveData.value = false
                    val post = result.data
                    val isAdminGroup =
                        post.group?.role == PostGroupModel.Role.OWNER || post.group?.role == PostGroupModel.Role.ADMINISTRATOR
                    _isNotificationVisibleLiveData.value = isAdminGroup

                    groupId = post.group?.id

                    feedItems.setAllPosts(
                        listOf(
                            post.shallowCopy().also {
                                it.comments.clear()
                            }
                        ),
                        emptyList()
                    )
                    FeedPostUpdatedBusEvent(post.shallowCopy()).postEvent()

                    markSeenPost()

                    fetchNextComments(false)

                    showAutoKeyboardIfNeed()
                }
                is Result.Error -> {
                    _isSwipeRefreshLayoutVisibleLiveData.value = false
                    _postErrorLiveData.value = result.exception
                    Timber.e(result.exception)
                }
            }
        }
        fetchPostJob?.invokeOnCompletion {
            _isSwipeRefreshLayoutVisibleLiveData.value = false
        }
    }

    fun fetchMoreComments() {
        if (nextCommentsQueries.isEmpty()) return
        fetchNextComments(true)
    }

    private fun fetchNextComments(isFetchMore: Boolean) {
        if (!isFetchMore) {
            nextCommentsQueries.clear()
            nextCommentsQueries.putAll(defaultCommentsQueries)
        }
        fetchCommentsJob = viewModelScope.launch {
            when (val result = postUseCaseFacade.fetchCommentsUseCase(nextCommentsQueries)) {
                is Result.Success -> {
                    val commentModels = result.data.first
                    val previousQueries = result.data.second
                    val nextQueries = result.data.third
                    previousCommentQueries.clear()
                    previousCommentQueries.putAll(previousQueries)
                    nextCommentsQueries.clear()
                    nextCommentsQueries.putAll(nextQueries)
                    if (previousQueries.isNotEmpty()) {
                        commentModels.map {
                            it.hasPreviousComment = true
                        }
                    }
                    val postHasNewComment = feedItems.addAllComments(
                        postId,
                        commentModels,
                        PostCommentModel.Target(postId, PostCommentModel.Target.Type.POST)
                    )

                    if (postHasNewComment != null) {
                        onPostUpdated(FeedPostUpdatedBusEvent(postHasNewComment))
                    }
                }
                is Result.Error -> {
                    if (!isFetchMore) {
                        feedItems.setLiveData()
                    }
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun fetchPreviousComment() {
        fetchCommentsJob = viewModelScope.launch {
            when (val result = postUseCaseFacade.fetchCommentsUseCase(previousCommentQueries)) {
                is Result.Success -> {
                    val commentModels = result.data.first
                    val previousQueries = result.data.second
                    previousCommentQueries.clear()
                    previousCommentQueries.putAll(previousQueries)
                    if (previousQueries.isNotEmpty()) {
                        commentModels.map {
                            it.hasPreviousComment = true
                        }
                    }
                    val postHasNewComment = feedItems.addAllPreviousComments(
                        postId,
                        commentModels,
                        PostCommentModel.Target(postId, PostCommentModel.Target.Type.POST)
                    )

                    if (postHasNewComment != null) {
                        onPostUpdated(FeedPostUpdatedBusEvent(postHasNewComment))
                    }
                }
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun getSubjectTypeViewContentEvent(postId: String): String? {
        return feedItems.getPost(postId)?.getSubjectTypeViewContentEvent(myUserId)
    }

    fun copyCommentContent(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        application.clipTextContentToShare(commentModel.text)

        GAPOAnalytics.getInstance(application)
            .logEventCopyContent(
                postId,
                ObjectType.COMMENT,
                screenName = GAPOAnalyticsEvents.SCREEN_POST_DETAILS
            )
    }

    fun deleteComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        viewModelScope.launch {
            val comment = feedItems.getComment(postId, commentId, parentCommentId, target)
            when (val result = postUseCaseFacade.deleteCommentByIdUseCase(commentId)) {
                is Result.Success -> {
                    FeedCommentDeletedBusEvent(
                        postId,
                        commentId,
                        parentCommentId,
                        target,
                        comment?.replyCount ?: 0L
                    ).postEvent()
                }
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun switchReplyCommentMode(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        switchReplyCommentMode(commentModel, parentCommentId ?: commentId)
    }

    fun switchEditCommentMode(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) {
        val commentModel =
            feedItems.getComment(postId, commentId, parentCommentId, target) ?: return
        switchEditCommentMode(commentModel, parentCommentId)
    }

    fun getPost(postId: String) = feedItems.getPost(postId)

    fun getSharedPost(parentPostId: String, postId: String) =
        feedItems.getSharedPost(parentPostId, postId)

    fun getPostOrFirstSharedPost(postId: String) = feedItems.getPostOrFirstSharedPost(postId)

    fun getComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) = feedItems.getComment(postId, commentId, parentCommentId, target)

    private fun markSeenPost() {
        viewModelScope.launch {
            when (val result = postUseCaseFacade.markSeenPostUseCase(postId)) {
                is Result.Success -> {}
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun showAutoKeyboardIfNeed() {
        viewModelScope.launch {
            delay(50)
            if (params.isAutoShowKeyboardWhenNav) {
                openKeyboard()
                params.isAutoShowKeyboardWhenNav = false
            }
        }
    }
}
