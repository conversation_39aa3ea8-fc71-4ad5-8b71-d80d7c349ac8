package com.gg.gapo.feature.invitation.presentation

import android.os.Bundle
import android.widget.Toast
import com.gg.gapo.core.eventbus.invite.InvitationCreateChatGroupSuccessBusEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.feature.invitation.databinding.InvitationWorkspaceActivityBinding
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.viewModel

@AppDeepLink("invitation_workspace")
internal class InvitationWorkspaceActivity : GapoThemeBaseActivity() {

    private val viewModel: InvitationWorkspaceViewModel by viewModel()

    private lateinit var binding: InvitationWorkspaceActivityBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = InvitationWorkspaceActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        viewModel.parseData(intent)
        registerEventBus()

        viewModel.isShowMessageNeedApproveLiveData.observe(this) {
            if (it) {
                GapoToast.makeNormal(
                    application,
                    application.getString(GapoStrings.messenger_add_member_require_approval),
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onCreateGroupChatSuccess(event: InvitationCreateChatGroupSuccessBusEvent) {
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterEventBus()
    }
}
