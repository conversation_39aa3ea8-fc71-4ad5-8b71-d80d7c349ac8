package com.gg.gapo.feature.invitation.presentation

import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.gg.gapo.core.eventbus.chat.MessengerSettingDetailsBusEvent
import com.gg.gapo.core.eventbus.group.GroupDetailRefreshBusEvent
import com.gg.gapo.core.eventbus.invite.InvitationMemberSelectedBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.bottomsheet.GapoAlertBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.invitation.R
import com.gg.gapo.feature.invitation.databinding.InvitationWorkspaceFragmentBinding
import com.gg.gapo.feature.invitation.domain.organization.model.DataOrganization
import com.gg.gapo.feature.invitation.presentation.adapter.InvitationPagerAdapter
import com.gg.gapo.feature.invitation.presentation.create.InvitationWorkspaceCreateGroupChatActivity
import com.gg.gapo.feature.invitation.presentation.department.model.mapToDomain
import com.gg.gapo.feature.invitation.presentation.member.InvitationWorkspaceMemberFragment
import com.gg.gapo.feature.invitation.presentation.member.model.mapToDomain
import com.gg.gapo.feature.invitation.presentation.role.model.mapToDomain
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.gson.Gson
import org.greenrobot.eventbus.EventBus
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber

internal class InvitationWorkspaceFragment : Fragment() {

    private var binding by autoCleared<InvitationWorkspaceFragmentBinding>()
    private var invitationPagerAdapter: InvitationPagerAdapter? = null
    private val viewModel by sharedViewModel<InvitationWorkspaceViewModel>()

    enum class InvitationWorkspaceType(val value: Int, val content: String) {
        MEMBER(0, "member");

        companion object {
            fun valueOf(value: Int): InvitationWorkspaceType? =
                entries.find { it.value == value }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = InvitationWorkspaceFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        init(binding)
        setupPager()
    }

    private fun init(binding: InvitationWorkspaceFragmentBinding) {
        binding.invite.text = when {
            viewModel.isFromGroup() -> {
                getString(GapoStrings.shared_invite)
            }
            viewModel.threadId.isNotEmpty() -> {
                getString(GapoStrings.messenger_group_chat_settings_privacy_settings_add_member)
            }
            else -> {
                getString(GapoStrings.shared_continue)
            }
        }

        binding.btnBack.setDebouncedClickListener {
            if (!findNavController().popBackStack()) {
                activity?.finish()
            }
        }

        binding.invite.setDebouncedClickListener {
            if (viewModel.threadId.isEmpty() && viewModel.groupId.isEmpty()) {
                startActivity(
                    InvitationWorkspaceCreateGroupChatActivity.newIntent(
                        requireContext(),
                        viewModel.getSelectedList()
                    )
                )
                if (viewModel.selectedType == InvitationWorkspaceType.MEMBER) {
                    childFragmentManager.fragments.firstOrNull { it is InvitationWorkspaceMemberFragment }
                        ?.let {
                            (it as InvitationWorkspaceMemberFragment).clearViewModel()
                        }
                }
                return@setDebouncedClickListener
            }
            viewModel.onClickInvite()
        }

        binding.btnDone.setDebouncedClickListener {
            try {
                val data = DataOrganization(
                    viewModel.membersSelected.mapToDomain(),
                    viewModel.departmentsSelected.mapToDomain(),
                    viewModel.rolesSelected.mapToDomain()
                )
                val json = Gson().toJson(data)
                InvitationMemberSelectedBusEvent(json).postEvent()
                activity?.finish()
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

        viewModel.onInvitationResult.observe(
            viewLifecycleOwner,
            EventObserver {
                if (it) {
                    if (viewModel.isGroupLink()) {
                        val description =
                            getString(
                                GapoStrings.invitation_workspace_sent_success_description,
                                viewModel.title
                            )
                        GapoAlertBottomSheetFragment.Builder()
                            .setIconRes(R.drawable.ic_invitation_workspace_add_success)
                            .setTitleRes(GapoStrings.invitation_workspace_sent_success_title)
                            .setDescription(
                                SpannableStringBuilder(description).apply {
                                    this.setSpan(
                                        StyleSpan(Typeface.BOLD),
                                        description.indexOf(viewModel.title),
                                        description.indexOf(viewModel.title) + viewModel.title.length,
                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                    )
                                }
                            )
                            .setFirstButtonStylesRes(GapoStyles.GapoButton_Medium_BgSecondary)
                            .setFirstButtonTextRes(GapoStrings.shared_understood)
                            .setListener(object : GapoAlertBottomSheetFragment.Listener {
                                override fun onClickFirstButton(companionObject: Any?) {
                                    EventBus.getDefault().post(GroupDetailRefreshBusEvent())
                                    activity?.finish()
                                }

                                override fun onClickSecondButton(companionObject: Any?) {}
                            })
                            .create()
                            .show(childFragmentManager, "InvitationWorkspaceActivity")
                    } else {
                        activity?.finish()
                    }
                    EventBus.getDefault().post(MessengerSettingDetailsBusEvent())
                }
            }
        )

        viewModel.onInvitationFailure.observe(viewLifecycleOwner) { it ->
            it.consume()?.takeIf { it.first == InvitationWorkspaceType.MEMBER }?.let {
                MaterialAlertDialogBuilder(requireContext(), GapoStyles.GapoDialog_Alert)
                    .setMessage(it.second)
                    .setPositiveButton(GapoStrings.shared_understood) { dialog, _ -> dialog.dismiss() }
                    .show()
            }
        }
    }

    private fun setupPager() {
        invitationPagerAdapter = InvitationPagerAdapter(this)
        with(binding) {
            viewPager.adapter = invitationPagerAdapter
            viewPager.isUserInputEnabled = false
            viewModel?.let { viewModel ->
                title.text = when {
                    viewModel.isFromSurvey() -> {
                        viewModel.title
                    }
                    viewModel.isFromGroup() -> {
                        getString(GapoStrings.invitation_workspace_toolbar_title)
                    }
                    viewModel.threadId.isNotEmpty() -> {
                        getString(GapoStrings.messenger_group_chat_settings_add_member)
                    }
                    else -> {
                        getString(GapoStrings.messenger_create_group)
                    }
                }
            }
        }
    }
}
