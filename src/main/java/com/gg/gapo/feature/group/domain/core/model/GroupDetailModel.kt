package com.gg.gapo.feature.group.domain.core.model

import com.gg.gapo.feature.group.domain.legacy.model.remote.CurrentUserStatus

/**
 * <AUTHOR>
 */
internal data class GroupDetailModel(
    val id: String,
    val name: String,
    val privacy: GroupPrivacy,
    val type: GroupType,
    val departmentFullPath: List<String>,
    val cover: String?,
    val coverThumbPattern: String?,
    val memberCount: Int,
    val description: String,
    val discoverability: GroupDiscoverability,
    val previewMembers: List<GroupPreviewMemberModel>,
    var groupFollowStatusType: GroupFollowStatusType?,
    val currentMember: GroupPreviewMemberModel?,
    val currentUserStatus: CurrentUserStatus,
    val postNeedApprove: Boolean,
    val alias: String,
    val totalPost: Int,
    val approveRequests: String,
    val hiddenManagers: Boolean,
    val hasRules: Boolean,
    val premiumStatus: String,
    val workspaceId: String,
    val workspaces: List<GroupWorkSpaceModel>,
    val threadChatId: String?,
    val isDefaultOfWs: Boolean,
    val totalSchedulePost: Int,
    val nextPostTimeStamp: String
)
