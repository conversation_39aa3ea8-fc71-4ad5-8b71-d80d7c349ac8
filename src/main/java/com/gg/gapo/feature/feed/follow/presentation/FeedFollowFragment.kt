package com.gg.gapo.feature.feed.follow.presentation

import android.Manifest
import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.core.view.doOnPreDraw
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.feed.common.provider.FeedSharedImageLoaderProvider
import com.gg.gapo.core.feed.common.provider.FeedSharedRecyclerViewPoolProvider
import com.gg.gapo.core.feed.common.provider.FeedSharedVideoPlayerProvider
import com.gg.gapo.core.feed.item.FeedAdapter
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.post.domain.PostGroupModel
import com.gg.gapo.core.feed.post.domain.PostModel
import com.gg.gapo.core.feed.tracker.FeedVisibilityTracker
import com.gg.gapo.core.feed.tracker.visibility.FeedVisibilityTrackerListener
import com.gg.gapo.core.feed.tracker.visibility.FeedVisibilityViewHolder
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.FeedVideoPlayer
import com.gg.gapo.core.feed.widget.FeedRecyclerView
import com.gg.gapo.core.feed.worker.cancelDownloadFile
import com.gg.gapo.core.feed.worker.startDownloadCommentFile
import com.gg.gapo.core.feed.worker.startDownloadFile
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.ask.AskDeepLink
import com.gg.gapo.core.navigation.deeplink.assignee.picker.AssigneePickerDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.FeedDeepLinkScreen
import com.gg.gapo.core.navigation.deeplink.feed.achievement.UserAchievementBadgeDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.comment.CommentDetailsSheetDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.combine.PostCombineDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.create.PostCreateMediaDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.create.PostCreateSource
import com.gg.gapo.core.navigation.deeplink.feed.post.create.PostCreateTextDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.details.*
import com.gg.gapo.core.navigation.deeplink.feed.post.poll.PostPollAddVoteDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.poll.PostPollVotedUsersDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.share.PostShareDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupAnnouncementDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupDetailsDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupSuggestedDeepLink
import com.gg.gapo.core.navigation.deeplink.hashtag.HashTagFeedDeepLink
import com.gg.gapo.core.navigation.deeplink.home.HomeDeepLink
import com.gg.gapo.core.navigation.deeplink.livestream.LiveStreamStreamerDeepLink
import com.gg.gapo.core.navigation.deeplink.livestream.LiveStreamViewerDeepLink
import com.gg.gapo.core.navigation.deeplink.meeting.MeetingCreateDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.photo.viewer.MediaViewerDeepLink
import com.gg.gapo.core.navigation.deeplink.search.SearchDeepLink
import com.gg.gapo.core.navigation.deeplink.sticker.StickerDetailsSheetDeepLink
import com.gg.gapo.core.navigation.deeplink.user.MyQuestionsDeepLink
import com.gg.gapo.core.navigation.deeplink.user.SuggestedFriendsDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileEditDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceSwitchDeepLink
import com.gg.gapo.core.navigation.system.openEmailComposer
import com.gg.gapo.core.navigation.system.openPhoneCaller
import com.gg.gapo.core.navigation.web.openWebBrowser
import com.gg.gapo.core.navigation.web.startPreviewBrowser
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.bottomsheet.GapoAlertBottomSheetFragment
import com.gg.gapo.core.ui.bottomsheet.lifecycleOwner
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbarSuccess
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.user.domain.model.UserProfileModel
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.bundle.putHeavyObject
import com.gg.gapo.core.utilities.bundle.putHeavyObjects
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.file.openFile
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.livedata.debounce
import com.gg.gapo.core.utilities.view.recyclerview.NoLimitRecycledViewPool
import com.gg.gapo.core.workspace.domain.model.WorkspaceSwitchedFrom
import com.gg.gapo.feature.feed.follow.R
import com.gg.gapo.feature.feed.follow.databinding.FeedFollowFragmentBinding
import com.gg.gapo.feature.feed.follow.presentation.eventbus.FeedFollowReloadDataBusEvent
import com.gg.gapo.feature.feed.follow.presentation.tracking.FeedFollowItemVisibilityTracker
import com.gg.gapo.feature.feed.follow.presentation.viewmodel.FeedFollowViewModel
import com.gg.gapo.tooltips.presentation.TooltipsManager
import com.gg.gapo.tooltips.presentation.features.TooltipsContext
import kohii.v1.core.MemoryMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 01/04/2021
 */
class FeedFollowFragment internal constructor() :
    Fragment(),
    SwipeRefreshLayout.OnRefreshListener {

    private val tooltipsManager by inject<TooltipsManager>(mode = LazyThreadSafetyMode.NONE)

    private val feedFollowViewModel by viewModel<FeedFollowViewModel>()

    private val feedVisibilityTracker = FeedVisibilityTracker()

    private val followItemVisibilityTracker by lazy(LazyThreadSafetyMode.NONE) {
        FeedFollowItemVisibilityTracker(requireContext(), feedFollowViewModel)
    }

    private val liveStreamPermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.isNotEmpty() && result.values.all { it }) {
                navByDeepLink(
                    LiveStreamStreamerDeepLink(
                        options = GapoDeepLink.Options(
                            bundle = bundleOf(
                                LiveStreamStreamerDeepLink.TARGET_ID_EXTRA to feedFollowViewModel.myUserId,
                                LiveStreamStreamerDeepLink.IS_GROUP_EXTRA to false,
                                LiveStreamStreamerDeepLink.PRIVACY_EXTRA to PostModel.Privacy.WORK.privacy
                            )
                        )
                    )
                )
            } else {
                onDeniedPermission()
            }
        }

    private val downloadAttachmentPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.isNotEmpty() && result.values.all { it }) {
                savedDownloadAttachmentRequestToAskPermission?.invoke()
                savedDownloadAttachmentRequestToAskPermission = null
            } else {
                onDeniedPermission()
            }
        }

    private var savedDownloadAttachmentRequestToAskPermission: (() -> Unit)? = null

    private var binding by autoCleared<FeedFollowFragmentBinding> {
        feedVisibilityTracker.detach(it.listFeedItems)
        it.listFeedItems.adapter = null
    }

    private var feedAdapter by autoCleared<FeedAdapter>()

    private lateinit var feedVideoPlayer: FeedVideoPlayer

    private lateinit var feedImageLoader: FeedImageLoader

    private val userProfileLiveDataObserver = Observer<UserProfileModel> {
        feedFollowViewModel.onDisplayNameOrAvatarUpdated(it)
    }

    private val feedItemsLiveDataDataObserver = Observer<List<FeedViewData>> {
        feedAdapter.submitList(it)
    }

    private val startPostInformForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val message =
                    result.data?.getStringExtra(PostInformUnseenPostDeepLink.MESSAGE_EXTRA)
                if (!message.isNullOrEmpty()) {
                    this.makeNormalSnackbarSuccess(message)?.showOnTop()
                }
            }
        }

    private val assigneePickerActivityForResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val output =
                    result.data?.parcelable<AssigneePickerDeepLink.Output>(AssigneePickerDeepLink.OUTPUT_EXTRA)
                navByDeepLink(
                    UserAchievementBadgeDeepLink(
                        GapoDeepLink.Options(
                            Bundle().apply {
                                putBoolean(UserAchievementBadgeDeepLink.CAN_EDIT_USERS_EXTRA, true)
                                putHeavyObjects(
                                    requireContext(),
                                    UserAchievementBadgeDeepLink.ACHIEVEMENT_USER_KEY,
                                    output?.selectedAssignees.orEmpty()
                                )
                            }
                        )
                    )
                )
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FeedFollowFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.feedFollowViewModel = feedFollowViewModel
        binding.glideRequest = GapoGlide.with(this)
        binding.swipeRefreshLayout.setOnRefreshListener(this)
        initRcvFeeds()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val viewLifecycleOwner = viewLifecycleOwner

        feedFollowViewModel.onClickWorkspaceEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        WorkspaceSwitchDeepLink(
                            options = GapoDeepLink.Options(
                                bundle = bundleOf(WorkspaceSwitchDeepLink.FROM_EXTRA to WorkspaceSwitchedFrom.FEED_FOLLOW.from)
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onClickButtonZoomEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(MeetingCreateDeepLink())
                }
            )

        feedFollowViewModel.onClickButtonSearchEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    GAPOAnalytics.getInstance(requireContext().applicationContext)
                        .logEventExploreSearch(GAPOAnalyticsEvents.SCREEN_FOLLOWING)
                    navByDeepLink(SearchDeepLink())
                }
            )

        feedFollowViewModel.onClickButtonMyProfileEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToMyProfile()
                }
            )

        feedFollowViewModel.onComposeClickOnOpenMyProfile
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToMyProfile()
                }
            )

        feedFollowViewModel.onComposeClickOnOpenPostCreator
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostCreateTextDeepLink(
                            options = GapoDeepLink.Options(
                                PostCreateTextDeepLink.createLinkBundle(
                                    PostCreateSource.FEED
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onComposeClickOnOpenLiveStreamCreator
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    liveStreamPermissionsLauncher.launch(LIVE_STREAM_PERMISSIONS)
                }
            )

        feedFollowViewModel.onComposeClickOnOpenPostMediaCreator
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostCreateMediaDeepLink(
                            options = GapoDeepLink.Options(
                                PostCreateMediaDeepLink.createLinkBundle(
                                    PostCreateSource.FEED
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onComposeClickOnOpenAssigneePickerForAchievement
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    assigneePickerActivityForResultLauncher.launch(
                        AssigneePickerDeepLink(
                            options = GapoDeepLink.Options(
                                bundle = bundleOf(
                                    AssigneePickerDeepLink.INPUT_EXTRA to AssigneePickerDeepLink.Input
                                        .Builder()
                                        .setTitle(getString(GapoStrings.achievement_add_members_title))
                                        .setActionButtonTitle(getString(GapoStrings.shared_continue))
                                        .setMode(AssigneePickerDeepLink.Input.Mode.SELECT_NEW)
                                        .setTabs(
                                            listOf(
                                                AssigneePickerDeepLink.Input.Tab.MEMBER
                                            )
                                        )
                                        .setArguments(
                                            AssigneePickerDeepLink.Input.Arguments(
                                                treeId = null,
                                                pickAllDepartment = null,
                                                ignoreMe = true,
                                                allowChangeTree = null,
                                                observerBackPress = null
                                            )
                                        )
                                        .setSConfigs(
                                            AssigneePickerDeepLink.Input.SConfigs(
                                                showBtnBack = true,
                                                showBtnDone = true,
                                                showBtnViewSelectedMembers = false,
                                                searchOnlyCurrentWorkspace = true,
                                                limitUsers = 20
                                            )
                                        )
                                        .build()
                                )
                            )
                        ).getIntent(requireContext())
                    )
                }
            )

        feedFollowViewModel.onAnnouncementPostClickOnViewAll
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(GroupAnnouncementDeepLink())
                }
            )

        feedFollowViewModel.onAnnouncementPostClickOnUserGuide
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver { isAdminWS ->
                    tooltipsManager.show(
                        requireActivity() as AppCompatActivity,
                        if (isAdminWS) TooltipsContext.WORKSPACE_ANNOUNCEMENT_ADMIN else TooltipsContext.WORKSPACE_ANNOUNCEMENT_MEMBER
                    )
                }
            )

        feedFollowViewModel.onAnnouncementPostClickOnOptions
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val isTopAnnouncementPost = feedFollowViewModel.indexOfFirstAnnouncementPost(it)
                    navByDeepLink(
                        PostActionsDeepLink(
                            it,
                            FeedDeepLinkScreen.FEED_FOLLOW,
                            GapoDeepLink.Options(
                                PostActionsDeepLink.createBundle(
                                    isAnnouncementPost = true,
                                    isTopAnnouncementPost = isTopAnnouncementPost
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onOnboardingClickOnTurnOff
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    GapoAlertBottomSheetFragment.Builder()
                        .setIconRes(R.drawable.feed_follow_ic_warning_64dp_red)
                        .setTitleRes(GapoStrings.feeds_follow_onboarding_turn_off_confirm_dialog_title)
                        .setDescriptionRes(GapoStrings.feeds_follow_onboarding_turn_off_confirm_dialog_message)
                        .setFirstButtonStylesRes(GapoStyles.GapoButton_Large_BgSecondary)
                        .setFirstButtonTextRes(GapoStrings.feeds_follow_onboarding_turn_off_confirm_dialog_negative)
                        .setSecondButtonStylesRes(GapoStyles.GapoButton_Large_AccentWorkSecondary)
                        .setSecondButtonTextRes(GapoStrings.feeds_follow_onboarding_turn_off_confirm_dialog_positive)
                        .setListener(object : GapoAlertBottomSheetFragment.Listener {
                            override fun onClickSecondButton(companionObject: Any?) {
                                feedFollowViewModel.turnOffOnboarding()
                            }
                        })
                        .create()
                        .lifecycleOwner(viewLifecycleOwner)
                        .show(childFragmentManager, GapoAlertBottomSheetFragment.TAG)
                }
            )

        feedFollowViewModel.onOnboardingClickOnCreatePost
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostCreateTextDeepLink(
                            options = GapoDeepLink.Options(
                                PostCreateTextDeepLink.createLinkBundle(
                                    PostCreateSource.FEED
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onOnboardingClickOnYourGroups
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        HomeDeepLink(
                            options = GapoDeepLink.Options(
                                bundleOf(HomeDeepLink.GROUPS_SELECTED_EXTRA to true)
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onOnboardingClickOnAddInformation
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        UserProfileEditDeepLink()
                    )
                }
            )

        feedFollowViewModel.onOnboardingClickOnChangeAvatar
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToMyProfile()
                }
            )

        feedFollowViewModel.onSuggestedFriendClickOnViewAll
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(SuggestedFriendsDeepLink())
                }
            )

        feedFollowViewModel.onSuggestedFriendClickOnProfile
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onSuggestedGroupClickOnViewAll
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(GroupSuggestedDeepLink())
                }
            )

        feedFollowViewModel.onGroupClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToGroupDetails(it)
                }
            )

        feedFollowViewModel.onPostHeaderClickOnAuthor
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onPostHeaderClickOnGroup
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToGroupDetails(it)
                }
            )

        feedFollowViewModel.onPostHeaderClickOnOptions
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostActionsDeepLink(
                            it,
                            FeedDeepLinkScreen.FEED_FOLLOW
                        )
                    )
                }
            )

        feedFollowViewModel.onPostHeaderClickOnNotification
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val intent = PostInformUnseenPostDeepLink(it).getIntent(requireContext())
                    startPostInformForResult.launch(intent)
                }
            )

        feedFollowViewModel.onPostHeaderClickOnUser
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onPostHeaderClickOnMoreTaggedUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val postModel =
                        feedFollowViewModel.getPostOrChildPostOrFirstSharedPost(it)
                            ?: return@EventObserver
                    navByDeepLink(
                        PostTaggedUsersDeepLink(
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    PostTaggedUsersDeepLink.USERS_EXTRA to ArrayList(
                                        postModel.taggedUsers
                                    )
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostHeaderClickOnMoreAchievementUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val postModel =
                        feedFollowViewModel.getPostOrChildPostOrFirstSharedPost(it)
                            ?: return@EventObserver
                    navByDeepLink(
                        PostTaggedUsersDeepLink(
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    PostTaggedUsersDeepLink.USERS_EXTRA to ArrayList(
                                        postModel.acknowledge?.targetUsers.orEmpty()
                                    )
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onCombinePostHeaderClickOnUser
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onCombinePostHeaderClickMoreUser
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostTaggedUsersDeepLink(
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    "user_ids" to it.joinToString(",")
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onCombinePostHeaderClickOnGroup
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToGroupDetails(it)
                }
            )

        feedFollowViewModel.onPostTextClickOnHashtag
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    GAPOAnalytics.getInstance(requireContext().applicationContext)
                        .logEventExploreTrending(GAPOAnalyticsEvents.SCREEN_FOLLOWING)
                    navByDeepLink(HashTagFeedDeepLink(it))
                }
            )

        feedFollowViewModel.onPostTextClickOnPhoneNumber
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openPhoneCaller(it)
                }
            )

        feedFollowViewModel.onPostTextClickOnEmail
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openEmailComposer(it)
                }
            )

        feedFollowViewModel.onPostTextClickOnWebUrl
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openWebBrowser(it)
                }
            )

        feedFollowViewModel.onPostTextClickOnMention
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onPostAttachmentClickOnAttachment
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openFile(it)
                }
            )

        feedFollowViewModel.onPostAttachmentClickOnDownload
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    savedDownloadAttachmentRequestToAskPermission = {
                        context?.startDownloadFile(
                            it.postId,
                            it.fileId,
                            it.fileName,
                            it.fileUrl
                        )
                        context?.startPreviewBrowser(it.fileUrl)
                    }

                    if (feedFollowViewModel.isFeatureDownloadEnabled) {
                        downloadAttachmentPermissionLauncher.launch(DOWNLOAD_ATTACHMENT_PERMISSION)
                    } else {
                        context?.startPreviewBrowser(it.fileUrl)
                    }
                }
            )

        feedFollowViewModel.onPostCommentClickAttachment
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openFile(it)
                }
            )

        feedFollowViewModel.onPostCommentAttachmentClickOnDownload.observe(
            viewLifecycleOwner,
            EventObserver {
                savedDownloadAttachmentRequestToAskPermission = {
                    context?.startDownloadCommentFile(
                        it.postId,
                        it.fileId,
                        it.fileName,
                        it.commentId,
                        it.parentCommentId,
                        it.target,
                        it.fileUrl
                    )
                    context?.startPreviewBrowser(it.fileUrl)
                }
                if (feedFollowViewModel.isFeatureDownloadEnabled) {
                    downloadAttachmentPermissionLauncher.launch(DOWNLOAD_ATTACHMENT_PERMISSION)
                } else {
                    context?.startPreviewBrowser(it.fileUrl)
                }
            }
        )

        feedFollowViewModel.onPostAttachmentClickOnCancelDownload
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    context?.cancelDownloadFile(it.fileId)
                }
            )

        feedFollowViewModel.onPostPollVoteClickOnVotedUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val postModel =
                        feedFollowViewModel.getPostOrChildPostOrFirstSharedPost(it.postId)
                            ?: return@EventObserver
                    val voteModel = postModel.pollVote?.votes?.find { vote -> vote.id == it.voteId }
                        ?: return@EventObserver
                    navByDeepLink(
                        PostPollVotedUsersDeepLink(
                            it.postId,
                            it.voteId,
                            GapoDeepLink.Options(
                                PostPollVotedUsersDeepLink.createBundle(voteModel.count)
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostPollVoteClickOnAddVote
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(PostPollAddVoteDeepLink(it))
                }
            )

        feedFollowViewModel.onPostMediaClickOnMedia
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToPostMedia(it.postId, it.parentPostId, it.mediaId)
                }
            )

        feedFollowViewModel.onPostMediaVideoWatchedLength
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val shouldTrackLiveStream =
                        feedFollowViewModel.getPostOrChildPostOrFirstSharedPost(it.postId)?.shouldTrackLiveStream
                            ?: false

                    GAPOAnalytics.getInstance(requireContext())
                        .logEventFinishVideo(
                            id = it.postId,
                            watchLength = it.watchedLength,
                            totalDuration = it.duration,
                            isSteam = shouldTrackLiveStream,
                            videoId = it.mediaId,
                            screenName = GAPOAnalyticsEvents.SCREEN_FOLLOWING
                        )
                }
            )

        feedFollowViewModel.onPostMediaVideoClickOnLiveStream
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(LiveStreamViewerDeepLink(it))
                }
            )

        feedFollowViewModel.onPostPreviewLinkClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openWebBrowser(it)
                }
            )

        feedFollowViewModel.onPostQuestionClickOnAskUser
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onPostLetAskMeClickOnAsk
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    if (it.userId == feedFollowViewModel.myUserId) {
                        navByDeepLink(MyQuestionsDeepLink())
                    } else {
                        navByDeepLink(
                            AskDeepLink(
                                GapoDeepLink.Options(
                                    bundleOf(
                                        AskDeepLink.ANSWER_USER_ID_EXTRA to it.userId,
                                        AskDeepLink.ANSWER_USER_DISPLAY_NAME_EXTRA to it.userDisplayName
                                    )
                                )
                            )
                        )
                    }
                }
            )

        feedFollowViewModel.onPostReactClickOnOpenReactedUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val post = feedFollowViewModel.getPost(it.postId) ?: return@EventObserver
                    navByDeepLink(
                        PostReactedUsersDeepLink(
                            PostReactedUsersDeepLink.POST_CONTEXT,
                            it.postId,
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    PostReactedUsersDeepLink.REACT_COUNT_EXTRA to post.reactCount
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostReactClickOnComment
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToPostDetails(it.postId)
                }
            )

        feedFollowViewModel.onPostReactClickOnShare
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val post =
                        feedFollowViewModel.getPost(it.postId) ?: feedFollowViewModel.getChildPost(
                            it.postId
                        ) ?: return@EventObserver
                    navByDeepLink(
                        PostShareOptionsDeepLink(
                            it.postId,
                            post.user.id,
                            post.group?.id,
                            fromScreen = FeedDeepLinkScreen.FEED_FOLLOW
                        )
                    )
                }
            )

        feedFollowViewModel.onPostReactClickOnStatisticCount
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToPostDetails(it.postId)
                }
            )

        feedFollowViewModel.onPostReactClickOnSeenCount
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(PostSeenUsersDeepLink(it.postId))
                }
            )

        feedFollowViewModel.onPostCommentClickOnAuthor
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToUserProfile(it)
                }
            )

        feedFollowViewModel.onPostCommentClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToPostDetails(it.postId, it.commentId)
                }
            )

        feedFollowViewModel.onPostCommentClickOnSticker
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(StickerDetailsSheetDeepLink(stickerId = it.stickerId))
                }
            )

        feedFollowViewModel.onPostCommentClickOnMedia
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        MediaViewerDeepLink(
                            GapoDeepLink.Options(
                                Bundle().apply {
                                    putHeavyObjects(
                                        requireContext(),
                                        MediaViewerDeepLink.MEDIA_VIEWER_MEDIA_DATA_KEY,
                                        listOf(
                                            MediaViewerDeepLink.Media.createSingleMedia(
                                                it.src,
                                                it.thumb,
                                                it.thumb
                                            )
                                        )
                                    )
                                    putInt(MediaViewerDeepLink.START_POSITION_EXTRA, 0)
                                }
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostCommentClickPreviewLink
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    requireActivity().openWebBrowser(it)
                }
            )

        feedFollowViewModel.onPostCommentClickOnOpenReactedUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val comment = feedFollowViewModel.getComment(
                        it.postId,
                        it.commentId,
                        it.parentCommentId,
                        it.target
                    ) ?: return@EventObserver
                    navByDeepLink(
                        PostReactedUsersDeepLink(
                            PostReactedUsersDeepLink.COMMENT_CONTEXT,
                            it.commentId,
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    PostReactedUsersDeepLink.REACT_COUNT_EXTRA to comment.reactCount
                                )
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostInputCommentClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToComment(it)
                }
            )

        feedFollowViewModel.onPostClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navToPostDetails(it)
                }
            )

        feedFollowViewModel.onPostChangeAvatarClickOnView
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(PostMediaDetailsHorizontalDeepLink(it.postId, it.mediaId))
                }
            )

        feedFollowViewModel.onReachedEndClickOnRefreshFeed
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    feedFollowViewModel.onFeedFollowReloadData(FeedFollowReloadDataBusEvent)
                }
            )

        feedFollowViewModel.onFeedFollowScrollToTopEventLiveData
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    binding.listFeedItems.scrollToTop()
                }
            )

        feedFollowViewModel.onShowSnackBarHiddenPostEvent
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    makeNormalSnackbarSuccess(getString(GapoStrings.post_hide_post_success_message))?.showOnTop()
                }
            )

        feedFollowViewModel.onClickButtonViewMoreCombinePostEventLiveData
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    navByDeepLink(
                        PostCombineDeepLink(
                            it.second,
                            GapoDeepLink.Options(
                                Bundle().apply {
                                    putHeavyObjects(
                                        requireContext(),
                                        PostCombineDeepLink.POST_IDS_EXTRA,
                                        it.third
                                    )
                                    it.first?.let { title ->
                                        putHeavyObject(
                                            requireContext(),
                                            PostCombineDeepLink.TITLE_POST_EXTRA,
                                            title
                                        )
                                    }
                                }
                            )
                        )
                    )
                }
            )

        feedFollowViewModel.onPostHeaderSharePostClickOnGroup.observe(
            viewLifecycleOwner,
            EventObserver { event ->
                navByDeepLink(
                    PostShareDeepLink(
                        GapoDeepLink.Options(
                            bundle = PostShareDeepLink.createGroupBundle(
                                data = event.groupList.map { it as PostGroupModel },
                                postId = event.postId,
                                isView = true,
                                defaultId = null,
                                targetId = event.targetId
                            )
                        )
                    )
                )
            }
        )
    }

    override fun onResume() {
        super.onResume()
        feedFollowViewModel.userProfileLiveData.observe(
            viewLifecycleOwner,
            userProfileLiveDataObserver
        )
        feedFollowViewModel.feedItemsLiveData
            .observe(viewLifecycleOwner, feedItemsLiveDataDataObserver)
        if (feedFollowViewModel.isFirstTimeDisplayed) {
            feedFollowViewModel.fetchData()
            binding.listFeedItems.doOnPreDraw {
                tooltipsManager.show(requireActivity() as AppCompatActivity, TooltipsContext.HOME)
                tooltipsManager.show(
                    requireActivity() as AppCompatActivity,
                    TooltipsContext.HOME_ZOOM
                )
            }
        }
        followItemVisibilityTracker.onResume()
        feedAdapter.rebindActiveVideoViewHolders()
    }

    override fun onPause() {
        super.onPause()
        followItemVisibilityTracker.onPause()
    }

    override fun onRefresh() {
        feedFollowViewModel.fetchData()
    }

    private fun initRcvFeeds() {
        val feedsSharedVideoPlayerProvider =
            requireContext().applicationContext as? FeedSharedVideoPlayerProvider
        require(feedsSharedVideoPlayerProvider != null)
        feedVideoPlayer = feedsSharedVideoPlayerProvider.sharedFeedVideoPlayer

        val feedsSharedRecyclerViewPoolProvider =
            requireActivity() as? FeedSharedRecyclerViewPoolProvider

        val feedsRecyclerViewPool: NoLimitRecycledViewPool
        val feedsNestedRecyclerViewViewPool: NoLimitRecycledViewPool

        if (feedsSharedRecyclerViewPoolProvider == null) {
            feedsRecyclerViewPool = NoLimitRecycledViewPool()
            feedsNestedRecyclerViewViewPool = NoLimitRecycledViewPool()
            feedImageLoader = GapoGlide.with(this)
        } else {
            feedsRecyclerViewPool = feedsSharedRecyclerViewPoolProvider.feedPostRecyclerViewPool
            feedsNestedRecyclerViewViewPool =
                feedsSharedRecyclerViewPoolProvider.feedNestedRecyclerViewViewPool
            val sharedImageLoaderProvider = requireActivity() as? FeedSharedImageLoaderProvider
            require(sharedImageLoaderProvider != null)

            feedImageLoader = sharedImageLoaderProvider.sharedFeedImageLoader
        }

        feedAdapter = FeedAdapter(
            requireContext(),
            feedsNestedRecyclerViewViewPool,
            feedImageLoader,
            feedVideoPlayer
        ).also { it.registerVideoPlayerManager(this, binding.listFeedItems, MemoryMode.NORMAL) }

        feedVisibilityTracker.attach(binding.listFeedItems)
        feedVisibilityTracker.addListener(object : FeedVisibilityTrackerListener {
            override fun onViewHolderVisibilityChanged(
                viewHolder: FeedVisibilityViewHolder,
                percentVisibleHeight: Float,
                percentVisibleWidth: Float,
                visibleHeight: Int,
                visibleWidth: Int
            ) {
                followItemVisibilityTracker.track(
                    viewHolder,
                    percentVisibleHeight,
                    percentVisibleWidth
                )
            }
        })

        binding.listFeedItems.apply {
            setRecycledViewPool(feedsRecyclerViewPool)

            setOnFeedFetchMoreDataListener(object :
                    FeedRecyclerView.OnFeedFetchMoreDataListener {
                    override val isFeedDataFetching: Boolean
                        get() = feedFollowViewModel.isFeedFollowFetching

                    override fun onFetchMoreFeedData() {
                        feedFollowViewModel.fetchMoreFeedFollow()
                    }
                })

            adapter = feedAdapter
        }
    }

    private fun navToMyProfile() {
        navToUserProfile(feedFollowViewModel.myUserId)
    }

    private fun navToUserProfile(userId: String) {
        navByDeepLink(
            UserProfileDeepLink(
                userId
            )
        )
    }

    private fun navToPostMedia(
        postId: String,
        parentPostId: String?,
        mediaId: String
    ) {
        val postModel = if (parentPostId.isNullOrEmpty()) {
            feedFollowViewModel.getPost(postId) ?: feedFollowViewModel.getChildPost(postId)
        } else {
            feedFollowViewModel.getSharedPost(parentPostId, postId)
        } ?: return
        if (postModel.isSingleMedia) {
            navByDeepLink(PostMediaDetailsHorizontalDeepLink(postId, mediaId))
        } else {
            navByDeepLink(PostMediaDetailsVerticalDeepLink(postId, mediaId))
        }
    }

    private fun navToPostDetails(
        postId: String,
        commentId: String? = null,
        isKeyboardShown: Boolean = false
    ) {
        navByDeepLink(
            PostDetailsDeepLink(
                postId,
                commentId.orEmpty(),
                GapoDeepLink.Options(bundle = bundleOf(PostDetailsDeepLink.IS_KEYBOARD_SHOWN_EXTRA to isKeyboardShown))
            )
        )
    }

    private fun navToGroupDetails(groupId: String) {
        navByDeepLink(GroupDetailsDeepLink(groupId))
    }

    private fun navToComment(postId: String, parentCommentId: String = "") {
        navByDeepLink(
            CommentDetailsSheetDeepLink(
                postId,
                parentCommentId,
                GapoDeepLink.Options(bundle = bundleOf(CommentDetailsSheetDeepLink.IS_KEYBOARD_SHOWN_EXTRA to true))
            )
        )
    }

    private fun onDeniedPermission() {
        GapoToast.makeNegative(
            requireContext(),
            GapoStrings.shared_permission_denied_msg
        ).show()
    }

    companion object {

        private val LIVE_STREAM_PERMISSIONS = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )

        private val DOWNLOAD_ATTACHMENT_PERMISSION =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) arrayOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_AUDIO,
                Manifest.permission.READ_MEDIA_VIDEO
            ) else arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)

        fun createInstance() = FeedFollowFragment()
    }
}
