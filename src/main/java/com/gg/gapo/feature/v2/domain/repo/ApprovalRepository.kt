package com.gg.gapo.feature.v2.domain.repo

import com.gg.gapo.feature.v2.domain.model.request.ApprovalGetListRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CancelRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CommentOrderRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditWatcherRequestModel
import com.gg.gapo.feature.v2.domain.model.request.HandleStateRequestModel
import com.gg.gapo.feature.v2.domain.model.request.NewStepWorkflowRequestModel
import com.gg.gapo.feature.v2.domain.model.request.SubmitFormRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TimekeepingLeaveRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TransferPermissionRequestModel
import com.gg.gapo.feature.v2.domain.model.request.WorkflowFetchLogsRequestModel
import com.gg.gapo.feature.v2.domain.model.response.ApprovalResponseModel
import com.gg.gapo.feature.v2.domain.model.response.chat.GroupChatModel
import com.gg.gapo.feature.v2.domain.model.response.department.DepartmentModel
import com.gg.gapo.feature.v2.domain.model.response.form.FormDetailModel
import com.gg.gapo.feature.v2.domain.model.response.form.FormRequestItemModel
import com.gg.gapo.feature.v2.domain.model.response.form.WorkflowActivityDetailModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.CategoryModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.CategoryWithFormsModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.FormCollapseModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.LastEditCategoryModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.FormRequesterModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.FormWorkflowModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.WorkflowLogsModel
import com.gg.gapo.feature.v2.domain.model.response.more.CurrencyModel
import com.gg.gapo.feature.v2.domain.model.response.signature.SignatureDigitalModel
import com.gg.gapo.feature.v2.domain.model.response.signature.SignatureRequestModel
import com.gg.gapo.feature.v2.domain.model.response.timekeeping.TimekeepingLeaveModel
import com.gg.gapo.feature.v2.domain.model.response.timekeeping.TimekeepingTotalWorkOffModel
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @since 12/16/22
 */
internal interface ApprovalRepository {
    suspend fun fetchCategoriesWithForms(): Flow<List<CategoryWithFormsModel>>

    suspend fun fetchRecentForms(): Flow<List<FormCollapseModel>>

    suspend fun fetchLastEditCategory(): Flow<LastEditCategoryModel?>

    suspend fun fetchDetailForm(id: String, type: String): Flow<FormDetailModel?>

    suspend fun submitForm(id: String, request: SubmitFormRequestModel): Flow<FormDetailModel?>

    suspend fun editForm(id: String, request: SubmitFormRequestModel): Flow<FormDetailModel?>

    suspend fun fetchFormRequestsByType(request: ApprovalGetListRequestModel): Flow<ApprovalResponseModel<List<FormRequestItemModel>>>

    suspend fun handleTaskRequestState(request: HandleStateRequestModel): Flow<Any>

    suspend fun fetchDepartmentsByIds(queries: Map<String, String>): Flow<List<DepartmentModel>>

    suspend fun createGroupChat(id: String): Flow<GroupChatModel?>

    suspend fun createSignatureDigital(signature: SignatureRequestModel): Flow<SignatureDigitalModel?>

    suspend fun fetchSignatureDigital(): Flow<SignatureDigitalModel?>

    suspend fun editSignatureDigital(id: String): Flow<SignatureDigitalModel?>

    suspend fun fetchCurrencyList(): Flow<List<CurrencyModel>>

    suspend fun cancelRequestForm(request: CancelRequestModel): Flow<Boolean>

    suspend fun editFormByRequester(request: EditRequestModel): Flow<Boolean>

    suspend fun editWatchers(request: EditWatcherRequestModel): Flow<WorkflowLogsModel?>

    suspend fun commentOrder(request: CommentOrderRequestModel): Flow<WorkflowLogsModel?>

    suspend fun updateCommentOrder(id: String, request: CommentOrderRequestModel): Flow<WorkflowLogsModel?>

    suspend fun deleteCommentOrder(id: String): Flow<Boolean>

    suspend fun fetchParticipants(id: String): Flow<List<FormRequesterModel>>

    suspend fun fetchRuleOfViewer(id: String): Flow<String>

    suspend fun updateTransferApprovers(request: TransferPermissionRequestModel): Flow<Boolean>

    suspend fun updateStepWorkflow(request: NewStepWorkflowRequestModel): Flow<Boolean>

    suspend fun deleteStepWorkflow(id: String, stateId: String): Flow<Boolean>

    suspend fun fetchTimekeepingLeaveData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingLeaveModel?>

    suspend fun fetchTotalWorkOffData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingTotalWorkOffModel?>

    suspend fun fetchOverTimeData(request: TimekeepingLeaveRequestModel): Flow<Boolean>

    suspend fun handleTaskRequestStateAdvanceWorkflows(
        id: String,
        stateId: String,
        request: HandleStateRequestModel
    ): Flow<Any>

    suspend fun fetchActivitiesWorkflow(
        request: WorkflowFetchLogsRequestModel
    ): Flow<ApprovalResponseModel<List<WorkflowActivityDetailModel>>>

    suspend fun fetchLogsWorkflow(
        request: WorkflowFetchLogsRequestModel
    ): Flow<ApprovalResponseModel<List<WorkflowLogsModel>>>

    suspend fun fetchDetailWorkflow(
        id: String,
        stateId: String
    ): Flow<FormWorkflowModel?>

    suspend fun fetchCategories(): Flow<List<CategoryModel>>
}
