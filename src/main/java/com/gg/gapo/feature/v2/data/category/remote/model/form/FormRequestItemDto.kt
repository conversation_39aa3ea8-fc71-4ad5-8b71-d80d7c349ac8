package com.gg.gapo.feature.v2.data.category.remote.model.form

import com.gg.gapo.feature.v2.data.category.remote.model.ModelUtil.mapToLongValue
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowTriggerDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowTriggerMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.FormDetailFieldDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.FieldPermissionDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.FormRequesterDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.FormRequesterMapper.mapToDomainModel
import com.gg.gapo.feature.v2.domain.model.response.form.FormRequestItemModel
import com.gg.gapo.feature.v2.domain.model.response.form.field.FormDetailFieldModelMapper.mapToDomainModel
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils.getOrDefault
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 3/23/23
 */
internal data class FormRequestItemDto(
    @SerializedName("id")
    var id: String?,
    @SerializedName("type")
    var type: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("required")
    var required: Boolean?,
    @SerializedName("permission")
    var permission: FieldPermissionDto?,
    @SerializedName("code")
    val code: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("requester")
    val requester: FormRequesterDto?,
    @SerializedName("stateType")
    val stateType: String?,
    @SerializedName("processingStatus")
    val processingStatus: String?,
    @SerializedName("isRead")
    val isRead: Boolean?,
    @SerializedName("category")
    val category: String?,
    @SerializedName("fields")
    val fields: List<FormDetailFieldDto>?,
    @SerializedName("approve_trigger")
    val approveTrigger: List<WorkflowTriggerDto>?,
    @SerializedName("due_date")
    val dueDate: String?,
    @SerializedName("complete_time")
    val completeTime: String?
)

internal object FormRequestItemMapper {
    internal fun FormRequestItemDto.mapToDomainModel(): FormRequestItemModel {
        return FormRequestItemModel(
            id = id.orEmpty(),
            type = type.orEmpty(),
            name = name.orEmpty(),
            required = required.getOrDefault(false),
            permission = permission?.mapToDomainModel(),
            fields = fields?.mapNotNull {
                it.mapToDomainModel()
            }.orEmpty(),
            code = code.orEmpty(),
            status = status.orEmpty(),
            createdAt = createdAt.orEmpty(),
            requester = requester?.mapToDomainModel(),
            stateType = stateType.orEmpty(),
            processingStatus = processingStatus.orEmpty(),
            isRead = isRead.getOrDefault(false),
            category = category.orEmpty(),
            approvesTrigger = approveTrigger?.map { it.mapToDomainModel() }.orEmpty(),
            dueDate = dueDate?.mapToLongValue().getOrDefault(0L),
            completeTime = completeTime.orEmpty()
        )
    }
}
