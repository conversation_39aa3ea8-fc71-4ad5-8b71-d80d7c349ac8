package com.gg.gapo.feature.v2.presentation.order.adapter.form.holder

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.feature.approval.databinding.OrderFormListItemBinding
import com.gg.gapo.feature.v2.presentation.model.enum.ErrorFieldTypeEnum
import com.gg.gapo.feature.v2.presentation.model.enum.ErrorFieldTypeEnum.Companion.isError
import com.gg.gapo.feature.v2.presentation.model.form.SelectOptionFieldViewData
import com.gg.gapo.feature.v2.presentation.model.form.value.ValueSelectOptionFieldViewData
import com.gg.gapo.feature.v2.presentation.order.adapter.form.FieldsAdapter.Companion.NAME_FIELD_PAYLOAD
import com.gg.gapo.feature.v2.presentation.order.adapter.form.FieldsAdapter.Companion.REQUIRED_FIELD_PAYLOAD
import com.gg.gapo.feature.v2.presentation.order.adapter.form.FieldsAdapter.Companion.TEXT_ERROR_FIELD_PAYLOAD
import com.gg.gapo.feature.v2.presentation.order.adapter.form.FieldsAdapter.Companion.VALUE_FIELD_PAYLOAD
import com.gg.gapo.feature.v2.presentation.order.adapter.form.FieldsAdapter.Companion.VALUE_IN_TABLE_PAYLOAD
import com.gg.gapo.feature.v2.presentation.order.adapter.form.SelectOneAdapter
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils

/**
 * <AUTHOR>
 * @since 2/3/23
 */

internal class OrderFormListSelectViewHolder(
    val context: Context,
    val binding: OrderFormListItemBinding,
    private val selectOnlyListener: (Int, String, Boolean) -> Unit
) : RecyclerView.ViewHolder(binding.root), OrderFormViewHolderInterface<SelectOptionFieldViewData> {
    private var adapter: SelectOneAdapter? = null

    override fun onBind(data: SelectOptionFieldViewData) = with(binding) {
        adapter = null
        adapter = SelectOneAdapter(data.id) { value, isChecked ->
            selectOnlyListener.invoke(bindingAdapterPosition, value, isChecked)
        }

        listItem.adapter = adapter
        loadTitle(data.name, data.required)
        loadValue(data.value)
        loadError(data.typeErrorEnum)
        showOrHideViewLineTable(data.valueInTable != null)
    }

    override fun onBindPayloads(data: SelectOptionFieldViewData, payloads: List<Any>) {
        if (payloads.contains(NAME_FIELD_PAYLOAD) || payloads.contains(
                REQUIRED_FIELD_PAYLOAD
            )
        ) {
            binding.loadTitle(data.name, data.required)
        }

        if (payloads.contains(VALUE_FIELD_PAYLOAD)) {
            loadValue(data.value)
        }

        if (payloads.contains(TEXT_ERROR_FIELD_PAYLOAD)) {
            binding.loadError(data.typeErrorEnum)
        }

        if (payloads.contains(VALUE_IN_TABLE_PAYLOAD)) {
            showOrHideViewLineTable(data.valueInTable != null)
        }
    }

    private fun OrderFormListItemBinding.loadTitle(name: String, required: Boolean) {
        textTitle.text = ApprovalUtils.appendRequireTextIfNeeded(
            context,
            name,
            required
        )
    }

    private fun loadValue(value: MutableList<ValueSelectOptionFieldViewData>) {
        adapter?.submitList(value.map { it.copy() })
    }

    private fun OrderFormListItemBinding.loadError(errorType: ErrorFieldTypeEnum) {
        textError.isVisible = errorType.isError()
        textError.text =
            if (errorType.isError()) context.getString(GapoStrings.new_approval_field_required_error_message) else ""
    }

    private fun showOrHideViewLineTable(isShow: Boolean) {
        binding.viewLineStart.isVisible = isShow
        binding.viewLineEnd.isVisible = isShow
    }
}
