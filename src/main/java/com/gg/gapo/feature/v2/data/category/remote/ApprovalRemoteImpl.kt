package com.gg.gapo.feature.v2.data.category.remote

import com.gg.gapo.feature.v2.data.category.remote.api.ApprovalApiService
import com.gg.gapo.feature.v2.data.category.remote.api.DepartmentApiService
import com.gg.gapo.feature.v2.data.category.remote.api.TimeKeepingApiService
import com.gg.gapo.feature.v2.data.category.remote.model.ApprovalResponse
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryLastEditDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryWithFormDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.FormCollapseDto
import com.gg.gapo.feature.v2.data.category.remote.model.chat.GroupChatDto
import com.gg.gapo.feature.v2.data.category.remote.model.department.DepartmentDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormDetailDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormRequestItemDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WatcherMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowActivityDetailDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowLogsDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.ParticipantDto
import com.gg.gapo.feature.v2.data.category.remote.model.more.CurrencyDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.CancelMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.CommentOrderMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.EditFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.EditWatcherRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.FetchTemplateFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.HandleStateRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.NewStepMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureEditRequest
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.SubmitFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.SubmitFormRequestMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.TransferPermissionMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.fields.SubmitFieldMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.signature.SignatureDigitalDto
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingLeaveDto
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingTotalWorkOffDto
import com.gg.gapo.feature.v2.domain.model.request.ApprovalGetListRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CancelRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CommentOrderRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditWatcherRequestModel
import com.gg.gapo.feature.v2.domain.model.request.NewStepWorkflowRequestModel
import com.gg.gapo.feature.v2.domain.model.request.SubmitFormRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TimekeepingLeaveRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TransferPermissionRequestModel
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils.getOrDefault
import com.google.gson.JsonObject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * <AUTHOR>
 * @since 11/30/22
 */
internal class ApprovalRemoteImpl(
    private val approvalService: ApprovalApiService,
    private val departmentApiService: DepartmentApiService,
    private val timeKeepingApiService: TimeKeepingApiService
) : ApprovalRemote, BaseApprovalRemoteImpl() {

    override suspend fun fetchCategoriesWithForms(): Flow<List<CategoryWithFormDto>> {
        return flow {
            val data = approvalService.fetchCategoriesWithForm().data.orEmpty()
            emit(data)
        }
    }

    override suspend fun fetchRecentForms(): Flow<List<FormCollapseDto>> {
        return flow {
            emit(approvalService.fetchRecentForms().data.orEmpty())
        }
    }

    override suspend fun fetchFormByIdAdminPermission(id: String): Flow<FormDetailDto?> {
        return flow {
            val response = approvalService.fetchFormByIdAdminPermission(id)
            emit(convertGsonToFormDto<FormDetailDto>(gson, response))
        }
    }

    override suspend fun fetchFormTemplateById(id: String): Flow<FormDetailDto?> {
        return flow {
            val response = approvalService.fetchFormTemplate(FetchTemplateFormRequestBody(id))
            emit(convertGsonToFormDto<FormDetailDto>(gson, response))
        }
    }

    override suspend fun submitForm(
        id: String,
        request: SubmitFormRequestModel
    ): Flow<FormDetailDto?> {
        return flow {
            val response = approvalService.submitRequestForm(
                id,
                request.mapToDto()
            )
            emit(convertGsonToFormDto<FormDetailDto>(gson, response))
        }
    }

    override suspend fun editForm(
        id: String,
        request: SubmitFormRequestModel
    ): Flow<FormDetailDto?> {
        return flow {
            val response = approvalService.editDetailForm(
                id = id,
                request = SubmitFormRequestBody(
                    name = request.name,
                    fields = request.fields.map {
                        it.mapToDto()
                    },
                    isDraft = request.isDraft,
                    workflows = null,
                    watchers = null,
                    dueDate = request.dueDate
                )
            )
            emit(convertGsonToFormDto<FormDetailDto>(gson, response))
        }
    }

    override suspend fun fetchLastEditCategory(): Flow<CategoryLastEditDto?> {
        return flow {
            emit(approvalService.fetchLastEditCategory().data)
        }
    }

    override suspend fun fetchDetailForm(id: String, type: String): Flow<FormDetailDto?> {
        return flow {
            val response = approvalService.fetchDetailForm(id, type)
            emit(convertGsonToFormDto<FormDetailDto>(gson, response))
        }
    }

    override suspend fun fetchFormRequestsByType(request: ApprovalGetListRequestModel): Flow<ApprovalResponse<List<FormRequestItemDto>>> {
        return flow {
            val response = request.run {
                approvalService.fetchFormRequestsByType(
                    type = type,
                    page = page,
                    limit = limit,
                    processingStatus = processingStatus?.key,
                    status = status,
                    from = from,
                    to = to,
                    categoryIds = categoryId,
                    search = searching,
                    userIds = userIds.joinToString(",")
                )
            }

            val dataResponse = gson.fromJson(response?.string(), JsonObject::class.java)
            val links = if (dataResponse.get(LINKS_JSON_MAPPER) != null) {
                gson.fromJson(
                    dataResponse.getAsJsonObject(LINKS_JSON_MAPPER),
                    ApprovalResponse.LinksDto::class.java
                )
            } else null

            val data = convertGsonToListFormDto<FormRequestItemDto>(gson, dataResponse)
            emit(
                ApprovalResponse(
                    data = data,
                    links = links,
                    code = 200,
                    message = null
                )
            )
        }
    }

    override suspend fun handleTaskRequestState(request: HandleStateRequestBody): Flow<Any> {
        return flow {
            emit(approvalService.handleTaskRequestState(request))
        }
    }

    override suspend fun fetchDepartmentsByIds(queries: Map<String, String>): Flow<List<DepartmentDto>> {
        return flow {
            emit(departmentApiService.fetchDepartmentsWithIds(queries).data.orEmpty())
        }
    }

    override suspend fun createGroupChat(id: String): Flow<GroupChatDto?> {
        return flow {
            emit(approvalService.createGroupChat(id).data)
        }
    }

    override suspend fun createSignatureDigital(signature: SignatureRequestBody): Flow<SignatureDigitalDto?> {
        return flow {
            emit(approvalService.createSignatureDigital(signature).data)
        }
    }

    override suspend fun fetchSignatureDigital(): Flow<SignatureDigitalDto?> {
        return flow {
            emit(approvalService.fetchSignatureDigital().data)
        }
    }

    override suspend fun editSignatureDigital(id: String): Flow<SignatureDigitalDto?> {
        return flow {
            emit(approvalService.editSignatureDigital(SignatureEditRequest(id, "")).data)
        }
    }

    override suspend fun fetchCurrencyList(): Flow<List<CurrencyDto>> {
        return flow {
            emit(approvalService.fetchCurrency().data.orEmpty())
        }
    }

    override suspend fun cancelRequestForm(request: CancelRequestModel): Flow<Boolean> {
        return flow {
            emit(approvalService.cancelRequest(request.id, request.mapToDto()).data != null)
        }
    }

    override suspend fun editRequestForm(request: EditRequestModel): Flow<Boolean> {
        return flow {
            val data = approvalService.editRequest(
                request.id,
                EditFormRequestBody(request.fields.map { it.mapToDto() })
            )
            emit(data.code == 200)
        }
    }

    override suspend fun submitWatchers(request: EditWatcherRequestModel): Flow<WorkflowLogsDto?> {
        return flow {
            val response = approvalService.submitWatchers(
                request.id,
                request.stateId,
                EditWatcherRequestBody(request.watchers.map { it.mapToDto() })
            )

            emit(convertGsonToFormDto<WorkflowLogsDto>(gson, response))
        }
    }

    override suspend fun commentOrder(request: CommentOrderRequestModel): Flow<WorkflowLogsDto?> {
        return flow {
            val response = approvalService.commentOrder(request.mapToDto())
            emit(convertGsonToFormDto<WorkflowLogsDto>(gson, response))
        }
    }

    override suspend fun updateCommentOrder(
        id: String,
        request: CommentOrderRequestModel
    ): Flow<WorkflowLogsDto?> {
        return flow {
            val response = approvalService.updateCommentOrder(
                id,
                request.mapToDto()
            )
            emit(convertGsonToFormDto<WorkflowLogsDto>(gson, response))
        }
    }

    override suspend fun deleteCommentOrder(id: String): Flow<Boolean> {
        return flow {
            val data = approvalService.deleteCommentOrder(
                id
            )

            emit(data.data != null)
        }
    }

    override suspend fun fetchParticipants(id: String): Flow<List<ParticipantDto>> {
        return flow {
            val data = approvalService.fetchParticipants(
                id
            )

            emit(data.data.orEmpty())
        }
    }

    override suspend fun fetchRuleOfViewer(id: String): Flow<String> {
        return flow {
            val data = approvalService.fetchRuleOfViewer(id)
            emit(data.data?.activity.orEmpty())
        }
    }

    override suspend fun updateTransferApprovers(request: TransferPermissionRequestModel): Flow<Boolean> {
        return flow {
            val data = approvalService.updateTransferApprovers(
                request.requestId,
                request.stateId,
                request.mapToDto()
            )

            emit(data.code == 200)
        }
    }

    override suspend fun updateNewStepWorkflow(request: NewStepWorkflowRequestModel): Flow<Boolean> {
        return flow {
            val data = approvalService.addNewStepWorkflow(
                request.requestId,
                request.stateId,
                request.mapToDto()
            )

            emit(data.code == 200)
        }
    }

    override suspend fun deleteNewStepWorkflow(id: String, stateId: String): Flow<Boolean> {
        return flow {
            val data = approvalService.deleteNewStepWorkflow(
                id,
                stateId
            )

            emit(data.code == 200)
        }
    }

    override suspend fun fetchLeaveData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingLeaveDto?> {
        return flow {
            val data = timeKeepingApiService.fetchLeaveData(
                request.userId,
                request.from,
                request.to
            )
            emit(data.data)
        }
    }

    override suspend fun fetchTotalWorkOffData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingTotalWorkOffDto?> {
        return flow {
            val data = timeKeepingApiService.fetchTotalWorkOffData(
                userId = request.userId,
                from = request.from,
                to = request.to,
                timeOffType = request.timeOffType.key
            ).data
            emit(data)
        }
    }

    override suspend fun fetchOverTimeData(request: TimekeepingLeaveRequestModel): Flow<Boolean> {
        return flow {
            val data = timeKeepingApiService.fetchOverTimeData(
                request.userId,
                request.from,
                request.to
            ).data

            emit(data.getOrDefault(false))
        }
    }

    override suspend fun handleTaskRequestStateAdvanceWorkflows(
        id: String,
        stateId: String,
        request: HandleStateRequestBody
    ): Flow<Any> {
        return flow {
            emit(approvalService.handleTaskRequestStateAdvanceWorkflows(id, stateId, request))
        }
    }

    override suspend fun fetchActivitiesWorkflow(
        id: String,
        stateId: String,
        limit: Int?,
        page: Int?,
        search: String?,
        status: List<Int>
    ): Flow<ApprovalResponse<List<WorkflowActivityDetailDto>>> {
        return flow {
            val response = approvalService.fetchActivitiesWorkflow(
                id = id,
                stateId = stateId,
                limit = limit,
                page = page,
                search = search,
                statuses = status.joinToString(",")
            )

            val dataResponse = gson.fromJson(response?.string(), JsonObject::class.java)

            val links = if (dataResponse.get(LINKS_JSON_MAPPER) != null) {
                gson.fromJson(
                    dataResponse.getAsJsonObject(LINKS_JSON_MAPPER),
                    ApprovalResponse.LinksDto::class.java
                )
            } else null
            emit(
                ApprovalResponse(
                    data = convertGsonToListFormDto<WorkflowActivityDetailDto>(gson, dataResponse),
                    links = links,
                    code = 200,
                    message = null
                )
            )
        }
    }

    override suspend fun fetchLogsWorkflow(
        id: String,
        stateId: String,
        limit: Int?,
        page: Int?,
        search: String?
    ): Flow<ApprovalResponse<List<WorkflowLogsDto>>> {
        return flow {
            val response = approvalService.fetchLogsWorkflow(
                id,
                stateId,
                limit,
                page,
                search
            )

            val dataResponse = gson.fromJson(response?.string(), JsonObject::class.java)

            val links = if (dataResponse.get(LINKS_JSON_MAPPER) != null) {
                gson.fromJson(
                    dataResponse.getAsJsonObject(LINKS_JSON_MAPPER),
                    ApprovalResponse.LinksDto::class.java
                )
            } else null
            emit(
                ApprovalResponse(
                    data = convertGsonToListFormDto<WorkflowLogsDto>(gson, dataResponse),
                    links = links,
                    code = 200,
                    message = null
                )
            )
        }
    }

    override suspend fun fetchDetailWorkflow(
        id: String,
        stateId: String
    ): Flow<FormWorkflowDto?> {
        return flow {
            val response = approvalService.fetchDetailWorkflow(
                id,
                stateId
            )

            emit(response.data)
        }
    }

    override suspend fun fetchCategories(): Flow<List<CategoryWithFormDto>> {
        return flow {
            val data = approvalService.fetchCategories().data.orEmpty()
            emit(data)
        }
    }
}
