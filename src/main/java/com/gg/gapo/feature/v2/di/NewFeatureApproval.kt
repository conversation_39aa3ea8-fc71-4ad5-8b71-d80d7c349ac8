package com.gg.gapo.feature.v2.di

import com.gg.gapo.core.utilities.di.apiBaseUrl
import com.gg.gapo.core.utilities.di.commonOkHttp
import com.gg.gapo.core.utilities.di.downloadOkHttp
import com.gg.gapo.core.utilities.di.noAuthenticatorDownloadOkHttp
import com.gg.gapo.core.utilities.di.uploadApiBaseUrl
import com.gg.gapo.core.utilities.di.uploadOkHttp
import com.gg.gapo.core.utilities.retrofit.RetrofitFactory
import com.gg.gapo.feature.v2.data.category.ApprovalRepositoryImpl
import com.gg.gapo.feature.v2.data.category.remote.ApprovalRemote
import com.gg.gapo.feature.v2.data.category.remote.ApprovalRemoteImpl
import com.gg.gapo.feature.v2.data.category.remote.api.ApprovalApiService
import com.gg.gapo.feature.v2.data.category.remote.api.DepartmentApiService
import com.gg.gapo.feature.v2.data.category.remote.api.TimeKeepingApiService
import com.gg.gapo.feature.v2.data.category.remote.gson.IgnoreFailureTypeAdapterFactory
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.FormDetailCustomDeserializer
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.FormDetailFieldDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.TableFieldDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.valuefield.MetaDataValueFieldDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.valuefield.MetaDataWorkflowDeserializer
import com.gg.gapo.feature.v2.data.user.UserRepositoryImpl
import com.gg.gapo.feature.v2.data.user.remote.UserRemote
import com.gg.gapo.feature.v2.data.user.remote.UserRemoteImpl
import com.gg.gapo.feature.v2.data.user.remote.api.UserApiService
import com.gg.gapo.feature.v2.domain.repo.ApprovalRepository
import com.gg.gapo.feature.v2.domain.usecase.CancelRequestUseCase
import com.gg.gapo.feature.v2.domain.usecase.ChangeStateTaskAdvanceWlUseCase
import com.gg.gapo.feature.v2.domain.usecase.ChangeStateTaskRequestUseCase
import com.gg.gapo.feature.v2.domain.usecase.CommentOrderUseCase
import com.gg.gapo.feature.v2.domain.usecase.CreateGroupChatUseCase
import com.gg.gapo.feature.v2.domain.usecase.CreateSignatureDigitalUseCase
import com.gg.gapo.feature.v2.domain.usecase.DeleteCommentOrderUseCase
import com.gg.gapo.feature.v2.domain.usecase.DeleteStepWorkflowUseCase
import com.gg.gapo.feature.v2.domain.usecase.EditFormUseCase
import com.gg.gapo.feature.v2.domain.usecase.EditSignatureDigitalUseCase
import com.gg.gapo.feature.v2.domain.usecase.EditWatcherUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchActivitiesWorkflowUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchCategoriesUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchCategoriesWithFormsUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchCurrencyUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchDepartmentsByIdsUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchDetailFormUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchDetailWorkflowUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchFormAdminPermissionUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchFormRequestsUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchFormTemplateByIdUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchLastEditCategoryUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchLogsWorkflowUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchOverTimeDataUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchParticipantsUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchRecentFormsUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchRuleOfViewerUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchSignatureDigitalUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchTimekeepingLeaveUseCase
import com.gg.gapo.feature.v2.domain.usecase.FetchTimekeepingTotalWorkOffUseCase
import com.gg.gapo.feature.v2.domain.usecase.FormEditByRequestUseCase
import com.gg.gapo.feature.v2.domain.usecase.SubmitFormUseCase
import com.gg.gapo.feature.v2.domain.usecase.UpdateCommentOrderUseCase
import com.gg.gapo.feature.v2.domain.usecase.UpdateNewStepWorkflowUseCase
import com.gg.gapo.feature.v2.domain.usecase.UpdateRequestTransferPermissionUseCase
import com.gg.gapo.feature.v2.domain.user.UserRepository
import com.gg.gapo.feature.v2.domain.user.exception.UserExceptionHandler
import com.gg.gapo.feature.v2.domain.user.exception.UserExceptionHandlerImpl
import com.gg.gapo.feature.v2.domain.user.usecase.FetchUsersWithSelectedIdsUseCase
import com.gg.gapo.feature.v2.presentation.order.OrderApprovalActivityViewModel
import com.gg.gapo.feature.v2.presentation.order.action.OrderActionViewModel
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.filter.OrderTaskFilterBottomSheetViewModel
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.select.OrderSelectBottomSheetViewModel
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.users.DetailUsersBottomSheetViewModel
import com.gg.gapo.feature.v2.presentation.order.cancel.CancelRequestViewModel
import com.gg.gapo.feature.v2.presentation.order.comment.CommentOrderViewModel
import com.gg.gapo.feature.v2.presentation.order.detail.viewmodel.OrderFormDetailViewModel
import com.gg.gapo.feature.v2.presentation.order.forms.ListFormViewModel
import com.gg.gapo.feature.v2.presentation.order.nested.history.WorkflowHistoryViewModel
import com.gg.gapo.feature.v2.presentation.order.nested.history.activities.HistoryActivitiesViewModel
import com.gg.gapo.feature.v2.presentation.order.nested.history.logs.HistoryLogsViewModel
import com.gg.gapo.feature.v2.presentation.order.step.WorkflowNewStepViewModel
import com.gg.gapo.feature.v2.presentation.order.tasks.OrderTaskListViewModel
import com.gg.gapo.feature.v2.presentation.order.tasks.approver.OrderTaskApproverChildViewModel
import com.gg.gapo.feature.v2.presentation.order.tasks.requester.OrderTaskRequesterChildViewModel
import com.gg.gapo.feature.v2.presentation.order.tasks.watcher.OrderTaskWatcherChildViewModel
import com.gg.gapo.feature.v2.presentation.order.transfer.TransferPermissionViewModel
import com.gg.gapo.feature.v2.presentation.service.ApprovalDownloadWorker
import com.gg.gapo.feature.v2.presentation.service.ApprovalUploadWorker
import com.gg.gapo.feature.v2.presentation.signature.detail.SignatureDetailViewModel
import com.google.gson.GsonBuilder
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.androidx.workmanager.dsl.worker
import org.koin.core.qualifier.named
import org.koin.dsl.module

/**
 * <AUTHOR>
 * @since 27/03/2023
 */
private const val GSON_APPROVAL_MODULE = "GSON_APPROVAL_MODULE"

val featureNewApproval = module {
    factory(qualifier = named(GSON_APPROVAL_MODULE)) {
        GsonBuilder().registerTypeAdapter(
            FormDetailFieldDto::class.java,
            FormDetailCustomDeserializer()
        ).registerTypeAdapter(
            MetaDataValueFieldDto::class.java,
            MetaDataWorkflowDeserializer()
        ).registerTypeAdapter(
            TableFieldDto.TableValueFieldDto::class.java,
            FormDetailCustomDeserializer.TableFieldDynamicKeyDeserializer()
        ).registerTypeAdapterFactory(IgnoreFailureTypeAdapterFactory()).create()
    }

    // V2
    factory {
        RetrofitFactory.create<ApprovalApiService>(
            apiBaseUrl,
            commonOkHttp
        )
    }

    factory {
        RetrofitFactory.create<DepartmentApiService>(
            apiBaseUrl,
            commonOkHttp
        )
    }

    factory {
        RetrofitFactory.create<TimeKeepingApiService>(
            apiBaseUrl,
            commonOkHttp
        )
    }

    factory { FetchFormTemplateByIdUseCase(get()) }

    factory<ApprovalRemote> {
        ApprovalRemoteImpl(
            get(),
            get(),
            get()
        )
    }
    factory<ApprovalRepository> {
        ApprovalRepositoryImpl(
            get(),
            get()
        )
    }

    factory { RetrofitFactory.create<UserApiService>(apiBaseUrl, commonOkHttp) }
    factory<UserRemote> { UserRemoteImpl(get()) }
    factory<UserRepository> { UserRepositoryImpl(get(), get()) }

    factory { FetchCategoriesWithFormsUseCase(get()) }
    factory { FetchRecentFormsUseCase(get()) }
    factory { SubmitFormUseCase(get()) }
    factory { FetchLastEditCategoryUseCase(get()) }
    factory { FetchDetailFormUseCase(get()) }
    factory { FetchFormRequestsUseCase(get()) }
    factory { ChangeStateTaskRequestUseCase(get()) }
    factory { ChangeStateTaskAdvanceWlUseCase(get()) }
    factory { FetchDepartmentsByIdsUseCase(get()) }
    factory { EditFormUseCase(get()) }
    factory { CreateGroupChatUseCase(get()) }
    factory { FetchFormAdminPermissionUseCase(get()) }
    factory { FetchUsersWithSelectedIdsUseCase(get(), get()) }
    factory { CreateSignatureDigitalUseCase(get()) }
    factory { FetchSignatureDigitalUseCase(get()) }
    factory { EditSignatureDigitalUseCase(get()) }
    factory { FetchCurrencyUseCase(get()) }
    factory { CancelRequestUseCase(get()) }
    factory { FormEditByRequestUseCase(get()) }
    factory { EditWatcherUseCase(get()) }
    factory { CommentOrderUseCase(get()) }
    factory { UpdateCommentOrderUseCase(get()) }
    factory { DeleteCommentOrderUseCase(get()) }
    factory { FetchParticipantsUseCase(get()) }
    factory { FetchRuleOfViewerUseCase(get()) }
    factory { UpdateRequestTransferPermissionUseCase(get()) }
    factory { UpdateNewStepWorkflowUseCase(get()) }
    factory { DeleteStepWorkflowUseCase(get()) }
    factory { FetchTimekeepingLeaveUseCase(get()) }
    factory { FetchTimekeepingTotalWorkOffUseCase(get()) }
    factory { FetchOverTimeDataUseCase(get()) }
    factory { FetchActivitiesWorkflowUseCase(get()) }
    factory { FetchLogsWorkflowUseCase(get()) }
    factory { FetchDetailWorkflowUseCase(get()) }
    factory { FetchCategoriesUseCase(get()) }

    factory<UserExceptionHandler> {
        UserExceptionHandlerImpl(
            get(),
            get(
                qualifier = named(
                    GSON_APPROVAL_MODULE
                )
            )
        )
    }

    viewModel { OrderTaskApproverChildViewModel(get(), get(), get()) }
    viewModel { parameters ->
        OrderActionViewModel(
            userManager = get(),
            workspaceManager = get(),
            requestData = parameters[0],
            dispatchers = get(),
            changeStateTaskRequestUseCase = get(),
            changeStateTaskRequestAdvanceWlUseCase = get(),
            editSignatureDigitalUseCase = get()
        )
    }
    viewModel { OrderTaskRequesterChildViewModel(get(), get(), get()) }
    viewModel { OrderTaskRequesterChildViewModel(get(), get(), get()) }
    viewModel { OrderTaskWatcherChildViewModel(get(), get(), get()) }
    viewModel { OrderTaskListViewModel(get()) }
    viewModel { OrderSelectBottomSheetViewModel(get()) }
    viewModel { ListFormViewModel(get(), get(), get(), get()) }
    viewModel { parameters ->
        OrderFormDetailViewModel(
            formId = parameters[0],
            fromType = parameters[1],
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }

    viewModel { parameters ->
        OrderTaskFilterBottomSheetViewModel(
            get(),
            get(),
            parameters[0],
            parameters[1],
            get()
        )
    }

    viewModel {
        SignatureDetailViewModel(get(), get(), get())
    }

    viewModel { OrderApprovalActivityViewModel(get(), get(), get()) }

    viewModel { CancelRequestViewModel(get(), get()) }

    viewModel {
        CommentOrderViewModel(get(), get(), get(), get(), get(), get())
    }

    viewModel {
        TransferPermissionViewModel(get(), get(), get())
    }

    viewModel { parameters ->
        WorkflowNewStepViewModel(parameters[0], parameters[1], get(), get(), get())
    }

    viewModel { parameters ->
        WorkflowNewStepViewModel(parameters[0], parameters[1], get(), get(), get())
    }

    viewModel {
        DetailUsersBottomSheetViewModel(get(), get())
    }

    viewModel {
        WorkflowHistoryViewModel(get())
    }

    viewModel {
        HistoryActivitiesViewModel(get(), get(), get())
    }

    viewModel {
        HistoryLogsViewModel(get(), get(), get(), get(), get(), get(), get())
    }

    worker { params ->
        ApprovalUploadWorker(
            androidContext(),
            params.get(),
            uploadApiBaseUrl,
            uploadOkHttp,
            get()
        )
    }

    worker { params ->
        ApprovalDownloadWorker(
            androidContext(),
            params.get(),
            downloadOkHttp,
            noAuthenticatorDownloadOkHttp,
            get()
        )
    }
}
