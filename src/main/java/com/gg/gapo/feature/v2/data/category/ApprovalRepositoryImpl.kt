package com.gg.gapo.feature.v2.data.category

import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.feature.v2.data.category.remote.ApprovalRemote
import com.gg.gapo.feature.v2.data.category.remote.model.ApprovalResponseMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryLastEditMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryMapper.mapToCategoryDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.category.FormCollapseMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.department.DepartmentMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormRequestItemMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowActivityDetailMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowLogsMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.ParticipantMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.more.CurrentDtoMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.request.HandleStateRequestMapper.mapToDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.UploadMapper.mapToMediaDto
import com.gg.gapo.feature.v2.data.category.remote.model.signature.SignatureDigitalDtoMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingLeaveMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingTotalWorkOffMapper.mapToModel
import com.gg.gapo.feature.v2.domain.model.request.ApprovalGetListRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CancelRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CommentOrderRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditWatcherRequestModel
import com.gg.gapo.feature.v2.domain.model.request.HandleStateRequestModel
import com.gg.gapo.feature.v2.domain.model.request.NewStepWorkflowRequestModel
import com.gg.gapo.feature.v2.domain.model.request.SubmitFormRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TimekeepingLeaveRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TransferPermissionRequestModel
import com.gg.gapo.feature.v2.domain.model.request.WorkflowFetchLogsRequestModel
import com.gg.gapo.feature.v2.domain.model.response.ApprovalResponseModel
import com.gg.gapo.feature.v2.domain.model.response.chat.GroupChatModel
import com.gg.gapo.feature.v2.domain.model.response.department.DepartmentModel
import com.gg.gapo.feature.v2.domain.model.response.form.FormDetailModel
import com.gg.gapo.feature.v2.domain.model.response.form.FormRequestItemModel
import com.gg.gapo.feature.v2.domain.model.response.form.WorkflowActivityDetailModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.CategoryModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.CategoryWithFormsModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.FormCollapseModel
import com.gg.gapo.feature.v2.domain.model.response.form.category.LastEditCategoryModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.FormRequesterModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.FormWorkflowModel
import com.gg.gapo.feature.v2.domain.model.response.form.more.WorkflowLogsModel
import com.gg.gapo.feature.v2.domain.model.response.more.CurrencyModel
import com.gg.gapo.feature.v2.domain.model.response.signature.SignatureDigitalModel
import com.gg.gapo.feature.v2.domain.model.response.signature.SignatureRequestModel
import com.gg.gapo.feature.v2.domain.model.response.timekeeping.TimekeepingLeaveModel
import com.gg.gapo.feature.v2.domain.model.response.timekeeping.TimekeepingTotalWorkOffModel
import com.gg.gapo.feature.v2.domain.repo.ApprovalRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @since 11/30/22
 */
internal class ApprovalRepositoryImpl(
    private val approvalRemote: ApprovalRemote,
    private val coroutineDispatchers: CoroutineDispatchers
) : ApprovalRepository {
    override suspend fun fetchCategoriesWithForms(): Flow<List<CategoryWithFormsModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchCategoriesWithForms().map { it ->
                it.map {
                    it.mapToDomainModel()
                }
            }
        }
    }

    override suspend fun fetchRecentForms(): Flow<List<FormCollapseModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchRecentForms().map { it ->
                it.map {
                    it.mapToDomainModel()
                }
            }
        }
    }

    override suspend fun fetchLastEditCategory(): Flow<LastEditCategoryModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchLastEditCategory().map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun fetchDetailForm(id: String, type: String): Flow<FormDetailModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchDetailForm(id, type).map { it?.mapToDomainModel() }
        }
    }

    override suspend fun submitForm(
        id: String,
        request: SubmitFormRequestModel
    ): Flow<FormDetailModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.submitForm(id, request).map { it?.mapToDomainModel() }
        }
    }

    override suspend fun editForm(
        id: String,
        request: SubmitFormRequestModel
    ): Flow<FormDetailModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.editForm(id, request).map { it?.mapToDomainModel() }
        }
    }

    override suspend fun fetchFormRequestsByType(request: ApprovalGetListRequestModel): Flow<ApprovalResponseModel<List<FormRequestItemModel>>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchFormRequestsByType(request).map { it ->
                ApprovalResponseModel(
                    data = it.data?.map { it.mapToDomainModel() }.orEmpty(),
                    links = it.links?.mapToDomainModel()
                )
            }
        }
    }

    override suspend fun handleTaskRequestState(request: HandleStateRequestModel): Flow<Any> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.handleTaskRequestState(
                request = request.mapToDto()
            )
        }
    }

    override suspend fun fetchDepartmentsByIds(queries: Map<String, String>): Flow<List<DepartmentModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchDepartmentsByIds(
                queries
            ).map { it ->
                it.map { it.mapToDomainModel() }
            }
        }
    }

    override suspend fun createGroupChat(id: String): Flow<GroupChatModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.createGroupChat(
                id
            ).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun createSignatureDigital(signature: SignatureRequestModel): Flow<SignatureDigitalModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.createSignatureDigital(
                SignatureRequestBody(
                    signature = signature.signature.mapToMediaDto()
                )
            ).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun fetchSignatureDigital(): Flow<SignatureDigitalModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchSignatureDigital().map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun editSignatureDigital(id: String): Flow<SignatureDigitalModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.editSignatureDigital(id).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun fetchCurrencyList(): Flow<List<CurrencyModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchCurrencyList().map { it ->
                it.map { it.mapToDomainModel() }
            }
        }
    }

    override suspend fun cancelRequestForm(request: CancelRequestModel): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.cancelRequestForm(request)
        }
    }

    override suspend fun editFormByRequester(request: EditRequestModel): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.editRequestForm(request)
        }
    }

    override suspend fun editWatchers(request: EditWatcherRequestModel): Flow<WorkflowLogsModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.submitWatchers(request).map { it?.mapToDomainModel() }
        }
    }

    override suspend fun commentOrder(request: CommentOrderRequestModel): Flow<WorkflowLogsModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.commentOrder(request).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun updateCommentOrder(
        id: String,
        request: CommentOrderRequestModel
    ): Flow<WorkflowLogsModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.updateCommentOrder(id, request).map { it?.mapToDomainModel() }
        }
    }

    override suspend fun deleteCommentOrder(id: String): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.deleteCommentOrder(id)
        }
    }

    override suspend fun fetchParticipants(id: String): Flow<List<FormRequesterModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchParticipants(id).map {
                it.map { it.mapToDomainModel() }
            }
        }
    }

    override suspend fun fetchRuleOfViewer(id: String): Flow<String> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchRuleOfViewer(id)
        }
    }

    override suspend fun updateTransferApprovers(request: TransferPermissionRequestModel): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.updateTransferApprovers(request)
        }
    }

    override suspend fun updateStepWorkflow(request: NewStepWorkflowRequestModel): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.updateNewStepWorkflow(request)
        }
    }

    override suspend fun deleteStepWorkflow(id: String, stateId: String): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.deleteNewStepWorkflow(id, stateId)
        }
    }

    override suspend fun fetchTimekeepingLeaveData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingLeaveModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchLeaveData(request).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun fetchTotalWorkOffData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingTotalWorkOffModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchTotalWorkOffData(request).map {
                it?.mapToModel()
            }
        }
    }

    override suspend fun fetchOverTimeData(request: TimekeepingLeaveRequestModel): Flow<Boolean> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchOverTimeData(request)
        }
    }

    override suspend fun handleTaskRequestStateAdvanceWorkflows(
        id: String,
        stateId: String,
        request: HandleStateRequestModel
    ): Flow<Any> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.handleTaskRequestStateAdvanceWorkflows(
                id,
                stateId,
                request = request.mapToDto()
            )
        }
    }

    override suspend fun fetchActivitiesWorkflow(request: WorkflowFetchLogsRequestModel): Flow<ApprovalResponseModel<List<WorkflowActivityDetailModel>>> {
        return withContext(coroutineDispatchers.io) {
            request.let {
                approvalRemote.fetchActivitiesWorkflow(
                    id = it.id,
                    stateId = it.stateId,
                    limit = it.limit,
                    page = it.page,
                    search = it.search,
                    status = it.status
                ).map {
                    ApprovalResponseModel(
                        data = it.data?.map { it.mapToDomainModel() }.orEmpty(),
                        links = it.links?.mapToDomainModel()
                    )
                }
            }
        }
    }

    override suspend fun fetchLogsWorkflow(request: WorkflowFetchLogsRequestModel): Flow<ApprovalResponseModel<List<WorkflowLogsModel>>> {
        return withContext(coroutineDispatchers.io) {
            request.let { it ->
                approvalRemote.fetchLogsWorkflow(
                    id = it.id,
                    stateId = it.stateId,
                    limit = it.limit,
                    page = it.page,
                    search = it.search
                ).map { it ->
                    ApprovalResponseModel(
                        data = it.data?.map { it.mapToDomainModel() }.orEmpty(),
                        links = it.links?.mapToDomainModel()
                    )
                }
            }
        }
    }

    override suspend fun fetchDetailWorkflow(
        id: String,
        stateId: String
    ): Flow<FormWorkflowModel?> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchDetailWorkflow(id, stateId).map {
                it?.mapToDomainModel()
            }
        }
    }

    override suspend fun fetchCategories(): Flow<List<CategoryModel>> {
        return withContext(coroutineDispatchers.io) {
            approvalRemote.fetchCategories().map { it ->
                it.map {
                    it.mapToCategoryDomainModel()
                }
            }
        }
    }
}
