package com.gg.gapo.feature.v2.data.category.remote

import com.gg.gapo.feature.v2.data.category.remote.model.ApprovalResponse
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryLastEditDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryWithFormDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.FormCollapseDto
import com.gg.gapo.feature.v2.data.category.remote.model.chat.GroupChatDto
import com.gg.gapo.feature.v2.data.category.remote.model.department.DepartmentDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormDetailDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormRequestItemDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowActivityDetailDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WorkflowLogsDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.ParticipantDto
import com.gg.gapo.feature.v2.data.category.remote.model.more.CurrencyDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.HandleStateRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.signature.SignatureDigitalDto
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingLeaveDto
import com.gg.gapo.feature.v2.data.category.remote.model.timekeeping.TimekeepingTotalWorkOffDto
import com.gg.gapo.feature.v2.domain.model.request.ApprovalGetListRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CancelRequestModel
import com.gg.gapo.feature.v2.domain.model.request.CommentOrderRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditRequestModel
import com.gg.gapo.feature.v2.domain.model.request.EditWatcherRequestModel
import com.gg.gapo.feature.v2.domain.model.request.NewStepWorkflowRequestModel
import com.gg.gapo.feature.v2.domain.model.request.SubmitFormRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TimekeepingLeaveRequestModel
import com.gg.gapo.feature.v2.domain.model.request.TransferPermissionRequestModel
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @since 11/30/22
 */
internal interface ApprovalRemote {

    suspend fun fetchCategoriesWithForms(): Flow<List<CategoryWithFormDto>>

    suspend fun fetchRecentForms(): Flow<List<FormCollapseDto>>

    suspend fun fetchFormByIdAdminPermission(id: String): Flow<FormDetailDto?>

    suspend fun fetchFormTemplateById(id: String): Flow<FormDetailDto?>

    suspend fun submitForm(id: String, request: SubmitFormRequestModel): Flow<FormDetailDto?>

    suspend fun editForm(id: String, request: SubmitFormRequestModel): Flow<FormDetailDto?>

    suspend fun fetchLastEditCategory(): Flow<CategoryLastEditDto?>

    suspend fun fetchDetailForm(id: String, type: String): Flow<FormDetailDto?>

    suspend fun fetchFormRequestsByType(request: ApprovalGetListRequestModel): Flow<ApprovalResponse<List<FormRequestItemDto>>>

    suspend fun handleTaskRequestState(request: HandleStateRequestBody): Flow<Any>

    suspend fun fetchDepartmentsByIds(queries: Map<String, String>): Flow<List<DepartmentDto>>

    suspend fun createGroupChat(id: String): Flow<GroupChatDto?>

    suspend fun createSignatureDigital(signature: SignatureRequestBody): Flow<SignatureDigitalDto?>

    suspend fun fetchSignatureDigital(): Flow<SignatureDigitalDto?>

    suspend fun editSignatureDigital(id: String): Flow<SignatureDigitalDto?>

    suspend fun fetchCurrencyList(): Flow<List<CurrencyDto>>

    suspend fun cancelRequestForm(request: CancelRequestModel): Flow<Boolean>

    suspend fun editRequestForm(request: EditRequestModel): Flow<Boolean>

    suspend fun submitWatchers(request: EditWatcherRequestModel): Flow<WorkflowLogsDto?>

    suspend fun commentOrder(request: CommentOrderRequestModel): Flow<WorkflowLogsDto?>

    suspend fun updateCommentOrder(id: String, request: CommentOrderRequestModel): Flow<WorkflowLogsDto?>

    suspend fun deleteCommentOrder(id: String): Flow<Boolean>

    suspend fun fetchParticipants(id: String): Flow<List<ParticipantDto>>

    suspend fun fetchRuleOfViewer(id: String): Flow<String>

    suspend fun updateTransferApprovers(request: TransferPermissionRequestModel): Flow<Boolean>

    suspend fun updateNewStepWorkflow(request: NewStepWorkflowRequestModel): Flow<Boolean>

    suspend fun deleteNewStepWorkflow(id: String, stateId: String): Flow<Boolean>

    suspend fun fetchLeaveData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingLeaveDto?>

    suspend fun fetchTotalWorkOffData(request: TimekeepingLeaveRequestModel): Flow<TimekeepingTotalWorkOffDto?>

    suspend fun fetchOverTimeData(request: TimekeepingLeaveRequestModel): Flow<Boolean>

    suspend fun handleTaskRequestStateAdvanceWorkflows(
        id: String,
        stateId: String,
        request: HandleStateRequestBody
    ): Flow<Any>

    suspend fun fetchActivitiesWorkflow(
        id: String,
        stateId: String,
        limit: Int?,
        page: Int?,
        search: String?,
        status: List<Int>
    ): Flow<ApprovalResponse<List<WorkflowActivityDetailDto>>>

    suspend fun fetchLogsWorkflow(
        id: String,
        stateId: String,
        limit: Int?,
        page: Int?,
        search: String?
    ): Flow<ApprovalResponse<List<WorkflowLogsDto>>>

    suspend fun fetchDetailWorkflow(
        id: String,
        stateId: String
    ): Flow<FormWorkflowDto?>

    suspend fun fetchCategories(): Flow<List<CategoryWithFormDto>>
}
