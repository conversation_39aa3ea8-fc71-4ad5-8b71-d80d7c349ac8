package com.gg.gapo.feature.v2.data.category.remote.model.form

import androidx.annotation.Keep
import com.gg.gapo.feature.v2.data.category.remote.model.ModelUtil.mapToLongValue
import com.gg.gapo.feature.v2.data.category.remote.model.form.FormDetailMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.ActivitiesMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.ActivitiesWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.CurrentStateDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.EdgesMapper.mapToDomainModel
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.EdgesWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormDesignDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormSettingsDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.PersonActionByDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WatcherDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.WatcherMapper.mapToDomainModelBySubmit
import com.gg.gapo.feature.v2.data.category.remote.model.form.field.FormDetailFieldDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.FormRequesterDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.FormRequesterMapper.mapToDomainModel
import com.gg.gapo.feature.v2.domain.model.StateRequestEnum
import com.gg.gapo.feature.v2.domain.model.enum.NodeTypeEnum
import com.gg.gapo.feature.v2.domain.model.response.form.FormDetailModel
import com.gg.gapo.feature.v2.domain.model.response.form.field.FormDetailFieldModelMapper.mapToDomainModel
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils.getOrDefault
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 12/22/22
 */
@Keep
internal data class FormDetailDto(
    @SerializedName("id")
    val id: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("category_id")
    val categoryID: String?,
    @SerializedName("workflow")
    val workflow: List<FormWorkflowDto>?,
    @SerializedName("workspace_id")
    val workspaceID: String?,
    @SerializedName("design")
    val design: FormDesignDto?,
    @SerializedName("settings")
    val settings: FormSettingsDto?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("is_draft")
    val isDraft: Boolean?,
    @SerializedName("created_by")
    val createdBy: PersonActionByDto?,
    @SerializedName("updated_by")
    val updatedBy: PersonActionByDto?,
    @SerializedName("created_at")
    val createdAt: String?,
    @SerializedName("updated_at")
    val updatedAt: String?,
    @SerializedName("isDeleted")
    val isDeleted: Boolean?,
    @SerializedName("user_id")
    val userId: String?,
    @SerializedName("category")
    val category: String?,
    @SerializedName("code")
    val code: String?,
    @SerializedName("isRequester")
    val isRequester: Boolean?,
    @SerializedName("progress")
    val progress: String?,
    @SerializedName("current_state")
    val currentState: CurrentStateDto?,
    @SerializedName("processingStatus")
    val processingStatus: String?,
    @SerializedName("first_step_name")
    val firstStepName: String?,
    @SerializedName("requester")
    val requester: FormRequesterDto?,
    @SerializedName("collab_id")
    val collabId: String?,
    @SerializedName("formStatus")
    val formStatus: String?,
    @SerializedName("form_id")
    val formId: String?,
    @SerializedName("fields")
    val fields: List<FormDetailFieldDto>?,
    @SerializedName("watchers")
    val watchers: List<WatcherDto>?,
    @SerializedName("link")
    val link: String?,
    @SerializedName("edges")
    val edges: List<EdgesWorkflowDto>?,
    @SerializedName("activities")
    val activities: List<ActivitiesWorkflowDto>?,
    @SerializedName("nodes")
    val nodes: List<Node>?,
    @SerializedName("due_date")
    val dueDate: String?,
    @SerializedName("complete_time")
    val completeTime: String?
) {
    internal data class Node(
        @SerializedName("id")
        val id: String?,
        @SerializedName("type")
        val type: String?,
        @SerializedName("positionAbsolute")
        val positionAbsolute: PositionAbsolute?
    ) {
        data class PositionAbsolute(
            @SerializedName("x")
            val x: Double?,
            @SerializedName("y")
            val y: Double?
        )
    }

    internal fun mapToDomainModel() =
        FormDetailModel(
            id = id.orEmpty(),
            name = name.orEmpty(),
            description = description.orEmpty(),
            fields = fields?.mapNotNull {
                it.mapToDomainModel()
            }.getOrDefault(emptyList()),
            categoryID = categoryID.orEmpty(),
            workflows = workflow?.map {
                it.mapToDomainModel()
            }.getOrDefault(emptyList()),
            workspaceID = workspaceID.orEmpty(),
            design = design?.mapToDomainModel(),
            settings = settings?.mapToDomainModel(),
            createdAt = createdAt.orEmpty(),
            createdBy = createdBy?.mapToDomainModel(),
            isDraft = isDraft.getOrDefault(false),
            status = status.orEmpty(),
            updatedAt = updatedAt.orEmpty(),
            updatedBy = updatedBy?.mapToDomainModel(),
            isDeleted = isDeleted.getOrDefault(false),
            userId = userId.orEmpty(),
            category = category.orEmpty(),
            code = code.orEmpty(),
            isRequester = isRequester.getOrDefault(false),
            progress = progress.orEmpty(),
            currentState = currentState?.mapToDomainModel(),
            processingStatus = StateRequestEnum.findState(processingStatus.orEmpty()),
            firstStepName = firstStepName.orEmpty(),
            requester = requester?.mapToDomainModel(),
            collabId = collabId.orEmpty(),
            formStatus = formStatus.orEmpty(),
            formId = formId.orEmpty(),
            watchers = watchers?.map {
                it.mapToDomainModelBySubmit()
            }.orEmpty().toMutableList(),
            link = link.orEmpty(),
            edges = edges?.map { edge ->
                val node = nodes?.find {
                    it.id == edge.source
                }
                edge.mapToDomainModel(
                    xPosition = node?.positionAbsolute?.x,
                    nodeType = NodeTypeEnum.findByType(node?.type)
                )
            }.orEmpty(),
            activities = activities?.map { it.mapToDomainModel() }.orEmpty(),
            nodes = nodes?.map { it.mapToDomainModel() }.orEmpty(),
            dueDate = dueDate?.mapToLongValue().getOrDefault(0L),
            completeTime = completeTime.getOrDefault("")
        )
}

internal object FormDetailMapper {
    fun FormDetailDto.Node.mapToDomainModel(): FormDetailModel.NodeModel {
        return FormDetailModel.NodeModel(
            id.orEmpty(),
            type.orEmpty()
        )
    }
}
