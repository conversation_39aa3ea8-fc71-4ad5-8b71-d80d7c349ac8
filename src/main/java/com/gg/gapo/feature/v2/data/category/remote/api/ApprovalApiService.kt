package com.gg.gapo.feature.v2.data.category.remote.api

import com.gg.gapo.core.utilities.api.GapoApiVersion
import com.gg.gapo.feature.v2.data.category.remote.model.ApprovalResponse
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryLastEditDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.CategoryWithFormDto
import com.gg.gapo.feature.v2.data.category.remote.model.category.FormCollapseDto
import com.gg.gapo.feature.v2.data.category.remote.model.chat.GroupChatDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.detail.FormWorkflowDto
import com.gg.gapo.feature.v2.data.category.remote.model.form.more.ParticipantDto
import com.gg.gapo.feature.v2.data.category.remote.model.more.CurrencyDto
import com.gg.gapo.feature.v2.data.category.remote.model.more.ViewerPermissionDto
import com.gg.gapo.feature.v2.data.category.remote.model.request.CancelFormRequest
import com.gg.gapo.feature.v2.data.category.remote.model.request.CommentOrderRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.EditFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.EditWatcherRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.FetchTemplateFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.HandleStateRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.NewStepWorkflowRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureEditRequest
import com.gg.gapo.feature.v2.data.category.remote.model.request.SignatureRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.SubmitFormRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.request.TransferPermissionRequestBody
import com.gg.gapo.feature.v2.data.category.remote.model.signature.SignatureDigitalDto
import okhttp3.ResponseBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * <AUTHOR>
 *
 * @since 11/30/22
 */
internal interface ApprovalApiService {
    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/forms")
    suspend fun fetchCategoriesWithForm(): ApprovalResponse<List<CategoryWithFormDto>?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/recent_forms")
    suspend fun fetchRecentForms(): ApprovalResponse<List<FormCollapseDto>?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/admin/forms/{id}")
    suspend fun fetchFormByIdAdminPermission(@Path("id") id: String): ResponseBody?

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/setup")
    suspend fun fetchFormTemplate(@Body request: FetchTemplateFormRequestBody): ResponseBody?

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}")
    suspend fun submitRequestForm(
        @Path("id") id: String,
        @Body request: SubmitFormRequestBody
    ): ResponseBody?

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}")
    suspend fun editDetailForm(
        @Path("id") id: String,
        @Body request: SubmitFormRequestBody
    ): ResponseBody?

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}")
    suspend fun fetchDetailForm(
        @Path("id") id: String,
        @Query("type") type: String
    ): ResponseBody?

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/last_edit")
    suspend fun fetchLastEditCategory(): ApprovalResponse<CategoryLastEditDto?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request")
    suspend fun fetchFormRequestsByType(
        @Query("type") type: String,
        @Query("category_ids") categoryIds: String? = null,
        @Query("status") status: String? = null,
        @Query("processing_status") processingStatus: String? = null,
        @Query("from") from: Long? = null,
        @Query("to") to: Long? = null,
        @Query("limit") limit: Int? = null,
        @Query("page") page: Int? = null,
        @Query("q") search: String? = null,
        @Query("user_ids") userIds: String? = null
    ): ResponseBody?

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/approve")
    suspend fun handleTaskRequestState(@Body request: HandleStateRequestBody): ApprovalResponse<Any?>

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/thread_chat")
    suspend fun createGroupChat(@Path("id") id: String): ApprovalResponse<GroupChatDto?>

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/signature")
    suspend fun createSignatureDigital(@Body id: SignatureRequestBody): ApprovalResponse<SignatureDigitalDto?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/signature")
    suspend fun fetchSignatureDigital(): ApprovalResponse<SignatureDigitalDto?>

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/signature")
    suspend fun editSignatureDigital(@Body request: SignatureEditRequest): ApprovalResponse<SignatureDigitalDto?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/currency")
    suspend fun fetchCurrency(): ApprovalResponse<List<CurrencyDto>?>

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/cancel")
    suspend fun cancelRequest(
        @Path("id") id: String,
        @Body comment: CancelFormRequest
    ): ApprovalResponse<Any?>

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/requester")
    suspend fun editRequest(
        @Path("id") id: String,
        @Body comment: EditFormRequestBody
    ): ApprovalResponse<Any?>

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/watchers/{state_id}")
    suspend fun submitWatchers(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Body watchers: EditWatcherRequestBody
    ): ResponseBody?

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/comment")
    suspend fun commentOrder(
        @Body request: CommentOrderRequestBody
    ): ResponseBody?

    @PUT("${GapoApiVersion.APPROVAL_API_VERSION}/api/comment/{id}")
    suspend fun updateCommentOrder(
        @Path("id") id: String,
        @Body request: CommentOrderRequestBody
    ): ResponseBody?

    @DELETE("${GapoApiVersion.APPROVAL_API_VERSION}/api/comment/{id}")
    suspend fun deleteCommentOrder(
        @Path("id") id: String
    ): ApprovalResponse<Any?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/participants")
    suspend fun fetchParticipants(
        @Path("id") id: String
    ): ApprovalResponse<List<ParticipantDto>?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/activity")
    suspend fun fetchRuleOfViewer(
        @Path("id") id: String
    ): ApprovalResponse<ViewerPermissionDto?>

    @PATCH("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/workflow/{state_id}")
    suspend fun updateTransferApprovers(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Body request: TransferPermissionRequestBody
    ): ApprovalResponse<Any?>

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/workflow/{state_id}")
    suspend fun addNewStepWorkflow(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Body request: NewStepWorkflowRequestBody
    ): ApprovalResponse<Any?>

    @DELETE("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/workflow/{state_id}")
    suspend fun deleteNewStepWorkflow(
        @Path("id") id: String,
        @Path("state_id") stateId: String
    ): ApprovalResponse<Any?>

    @POST("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/advance-flow/{state_id}/approve")
    suspend fun handleTaskRequestStateAdvanceWorkflows(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Body request: HandleStateRequestBody
    ): ApprovalResponse<Any?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/activities/{state_id}")
    suspend fun fetchActivitiesWorkflow(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Query("limit") limit: Int? = null,
        @Query("page") page: Int? = null,
        @Query("q") search: String? = null,
        @Query("status") statuses: String? = null
    ): ResponseBody?

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/logs/{state_id}")
    suspend fun fetchLogsWorkflow(
        @Path("id") id: String,
        @Path("state_id") stateId: String,
        @Query("limit") limit: Int? = null,
        @Query("page") page: Int? = null,
        @Query("q") search: String? = null
    ): ResponseBody?

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/{id}/workflow/{state_id}")
    suspend fun fetchDetailWorkflow(
        @Path("id") id: String,
        @Path("state_id") stateId: String
    ): ApprovalResponse<FormWorkflowDto?>

    @GET("${GapoApiVersion.APPROVAL_API_VERSION}/api/request/categories")
    suspend fun fetchCategories(): ApprovalResponse<List<CategoryWithFormDto>?>
}
