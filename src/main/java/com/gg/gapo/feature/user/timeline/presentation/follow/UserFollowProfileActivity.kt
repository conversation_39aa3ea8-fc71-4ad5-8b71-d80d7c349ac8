package com.gg.gapo.feature.user.timeline.presentation.follow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.user.R
import com.gg.gapo.feature.user.databinding.UserProfileFollowProfileActivityBinding
import com.gg.gapo.feature.user.timeline.presentation.follow.adapter.UserFollowProfileViewPagerAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

class UserFollowProfileActivity : GapoThemeBaseActivity() {

    private lateinit var binding: UserProfileFollowProfileActivityBinding

    private val userId by lazy {
        intent.getStringExtra(TAG).orEmpty()
    }

    private val viewModel by viewModel<UserFollowProfileViewModel> {
        parametersOf(this@UserFollowProfileActivity, userId)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = UserProfileFollowProfileActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        initLayout()
    }

    private fun initLayout() = with(binding) {
        val titles = arrayOf(
            getString(GapoStrings.app_following),
            getString(GapoStrings.user_timeline_profile_follow_count)
        )

        val fragments = listOf(
            UserFollowProfileFragment.newInstance(FollowType.FOLLOWING),
            UserFollowProfileFragment.newInstance(FollowType.FOLLOWER)
        )

        viewPager.adapter =
            UserFollowProfileViewPagerAdapter(this@UserFollowProfileActivity, fragments)
        TabLayoutMediator(tabs, viewPager, false, false) { tab, position ->
            tab.text = titles[position]
        }.attach()

        tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                viewPager.currentItem = tab.position
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
            }
        })
        viewPager.isUserInputEnabled = false
        viewPager.offscreenPageLimit = 2

        buttonBack.setDebouncedClickListener {
            finish()
        }
        viewModel.loadDataInit()
    }

    companion object {
        private const val TAG = "UserFollowProfileActivity"
        fun start(context: Context, userId: String) {
            context.startActivity(
                Intent(context, UserFollowProfileActivity::class.java).apply {
                    this.putExtra(TAG, userId)
                }
            )
        }
    }
}

internal enum class FollowType {
    FOLLOWING, FOLLOWER
}
