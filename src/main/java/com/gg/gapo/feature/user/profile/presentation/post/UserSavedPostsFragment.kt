package com.gg.gapo.feature.user.profile.presentation.post

import android.Manifest
import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.feed.common.provider.FeedSharedImageLoaderProvider
import com.gg.gapo.core.feed.common.provider.FeedSharedRecyclerViewPoolProvider
import com.gg.gapo.core.feed.common.provider.FeedSharedVideoPlayerProvider
import com.gg.gapo.core.feed.item.FeedAdapter
import com.gg.gapo.core.feed.post.domain.PostGroupModel
import com.gg.gapo.core.feed.post.domain.PostModel
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.FeedVideoPlayer
import com.gg.gapo.core.feed.widget.FeedRecyclerView
import com.gg.gapo.core.feed.worker.cancelDownloadFile
import com.gg.gapo.core.feed.worker.startDownloadFile
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.ask.AskDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.FeedDeepLinkScreen
import com.gg.gapo.core.navigation.deeplink.feed.post.details.*
import com.gg.gapo.core.navigation.deeplink.feed.post.poll.PostPollAddVoteDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.poll.PostPollVotedUsersDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.share.PostShareDeepLink
import com.gg.gapo.core.navigation.deeplink.hashtag.HashTagFeedDeepLink
import com.gg.gapo.core.navigation.deeplink.livestream.LiveStreamViewerDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.photo.viewer.MediaViewerDeepLink
import com.gg.gapo.core.navigation.deeplink.sticker.StickerDetailsSheetDeepLink
import com.gg.gapo.core.navigation.deeplink.user.MyQuestionsDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileDeepLink
import com.gg.gapo.core.navigation.system.openEmailComposer
import com.gg.gapo.core.navigation.system.openPhoneCaller
import com.gg.gapo.core.navigation.web.openWebBrowser
import com.gg.gapo.core.navigation.web.startPreviewBrowser
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbarSuccess
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.bundle.putHeavyObjects
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.file.openFile
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.recyclerview.NoLimitRecycledViewPool
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.user.databinding.UserSavedPostFragmentBinding
import com.gg.gapo.feature.user.profile.presentation.post.viewmodel.UserSavedPostViewModel
import com.gg.gapo.feature.user.timeline.utils.navToCommentActivity
import com.gg.gapo.feature.user.timeline.utils.navToPostDetailsActivity
import kohii.v1.core.MemoryMode
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 01/03/2023
 */
internal class UserSavedPostsFragment : Fragment(), SwipeRefreshLayout.OnRefreshListener {

    private var binding by autoCleared<UserSavedPostFragmentBinding> {
        it.listSavedPost.adapter = null
    }

    private var feedAdapter by autoCleared<FeedAdapter>()

    private lateinit var feedVideoPlayer: FeedVideoPlayer

    private lateinit var feedImageLoader: FeedImageLoader

    private val userSavedPostViewModel by viewModel<UserSavedPostViewModel>()

    private val downloadAttachmentPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.isNotEmpty() && result.values.all { it }) {
                savedDownloadAttachmentRequestToAskPermission?.invoke()
                savedDownloadAttachmentRequestToAskPermission = null
            } else {
                onDeniedPermission()
            }
        }

    private var savedDownloadAttachmentRequestToAskPermission: (() -> Unit)? = null

    private val startPostInformForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val message =
                    result.data?.getStringExtra(PostInformUnseenPostDeepLink.MESSAGE_EXTRA)
                if (!message.isNullOrEmpty()) {
                    this.makeNormalSnackbarSuccess(message)?.showOnTop()
                }
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = UserSavedPostFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = userSavedPostViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        userSavedPostViewModel.fetchData()
        initView()
        initRcvFeed()

        userSavedPostViewModel.feedItemsLiveData
            .observe(viewLifecycleOwner) {
                feedAdapter.submitList(it)
                binding.layoutEmpty.isVisible = it.isEmpty()
            }

        userSavedPostViewModel.onPostHeaderClickOnOptions
            .observe(
                viewLifecycleOwner,
                EventObserver { postId ->
                    val post = userSavedPostViewModel.getPost(postId)
                    navByDeepLink(
                        PostActionsDeepLink(
                            postId,
                            FeedDeepLinkScreen.FEED_USER_SAVED,
                            GapoDeepLink.Options(
                                PostActionsDeepLink.createBundle(
                                    isAvailabilityPost = post?.status == PostModel.Status.AVAILABILITY
                                )
                            )
                        )
                    )
                }
            )

        userSavedPostViewModel.onPostHeaderClickOnAuthor.observe(
            viewLifecycleOwner,
            EventObserver {
                navToUserProfile(it)
            }
        )

        userSavedPostViewModel.onPostHeaderClickOnUser.observe(
            viewLifecycleOwner,
            EventObserver {
                navToUserProfile(it)
            }
        )

        userSavedPostViewModel.onPostHeaderClickOnNotification.observe(
            viewLifecycleOwner,
            EventObserver { postId ->
                val post = userSavedPostViewModel.getPost(postId)
                post?.let {
                    if (post.status == PostModel.Status.AVAILABILITY) {
                        val intent = PostInformUnseenPostDeepLink(postId).getIntent(requireContext())
                        startPostInformForResult.launch(intent)
                    }
                }
            }
        )

        userSavedPostViewModel.onPostCommentClickOnOpenReactedUsers.observe(
            viewLifecycleOwner,
            EventObserver {
                val comment = userSavedPostViewModel.getComment(
                    it.postId,
                    it.commentId,
                    it.parentCommentId,
                    it.target
                ) ?: return@EventObserver
                navByDeepLink(
                    PostReactedUsersDeepLink(
                        PostReactedUsersDeepLink.COMMENT_CONTEXT,
                        it.commentId,
                        GapoDeepLink.Options(
                            bundle = bundleOf(
                                PostReactedUsersDeepLink.REACT_COUNT_EXTRA to comment.reactCount
                            )
                        )
                    )
                )
            }
        )

        userSavedPostViewModel.onPostReactClickOnShare.observe(
            viewLifecycleOwner,
            EventObserver {
                val post = userSavedPostViewModel.getPost(it.postId) ?: return@EventObserver
                navByDeepLink(
                    PostShareOptionsDeepLink(
                        it.postId,
                        post.user.id,
                        post.group?.id,
                        fromScreen = FeedDeepLinkScreen.FEED_FOLLOW
                    )
                )
            }
        )

        userSavedPostViewModel.onPostChangeAvatarClickOnView.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(PostMediaDetailsHorizontalDeepLink(it.postId, it.mediaId))
            }
        )

        userSavedPostViewModel.onPostReactClickOnStatisticCount.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().navToPostDetailsActivity(it.postId)
            }
        )

        userSavedPostViewModel.onPostReactClickOnSeenCount.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(PostSeenUsersDeepLink(it.postId))
            }
        )

        userSavedPostViewModel.onPostCommentClickOnAuthor.observe(
            viewLifecycleOwner,
            EventObserver {
                navToUserProfile(it)
            }
        )

        userSavedPostViewModel.onPostTextClickOnHashtag.observe(
            viewLifecycleOwner,
            EventObserver {
                GAPOAnalytics.getInstance(requireContext().applicationContext)
                    .logEventExploreTrending(GAPOAnalyticsEvents.SCREEN_FOLLOWING)
                navByDeepLink(HashTagFeedDeepLink(it))
            }
        )

        userSavedPostViewModel.onPostTextClickOnPhoneNumber.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().openPhoneCaller(it)
            }
        )

        userSavedPostViewModel.onPostTextClickOnEmail.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().openEmailComposer(it)
            }
        )

        userSavedPostViewModel.onPostTextClickOnWebUrl.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().openWebBrowser(it)
            }
        )

        userSavedPostViewModel.onPostTextClickOnMention.observe(
            viewLifecycleOwner,
            EventObserver {
                navToUserProfile(it)
            }
        )

        userSavedPostViewModel.onPostQuestionClickOnAskUser.observe(
            viewLifecycleOwner,
            EventObserver {
                navToUserProfile(it)
            }
        )

        userSavedPostViewModel.onPostInputCommentClickOnView.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().navToCommentActivity(it)
            }
        )

        userSavedPostViewModel.onPostMediaVideoClickOnLiveStream.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(LiveStreamViewerDeepLink(it))
            }
        )

        userSavedPostViewModel.onPostPreviewLinkClickOnView.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().openWebBrowser(it)
            }
        )

        userSavedPostViewModel.onPostAttachmentClickOnAttachment.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().openFile(it)
            }
        )

        userSavedPostViewModel.onPostAttachmentClickOnDownload.observe(
            viewLifecycleOwner,
            EventObserver {
                savedDownloadAttachmentRequestToAskPermission = {
                    context?.startDownloadFile(
                        it.postId,
                        it.fileId,
                        it.fileName,
                        it.fileUrl
                    )
                    context?.startPreviewBrowser(it.fileUrl)
                }

                if (userSavedPostViewModel.isFeatureDownloadEnabled) {
                    downloadAttachmentPermissionLauncher.launch(DOWNLOAD_ATTACHMENT_PERMISSION)
                } else {
                    context?.startPreviewBrowser(it.fileUrl)
                }
            }
        )

        userSavedPostViewModel.onPostAttachmentClickOnCancelDownload.observe(
            viewLifecycleOwner,
            EventObserver {
                context?.cancelDownloadFile(it.fileId)
            }
        )

        userSavedPostViewModel.onPostLetAskMeClickOnAsk.observe(
            viewLifecycleOwner,
            EventObserver {
                if (it.userId == userSavedPostViewModel.myUserId) {
                    navByDeepLink(MyQuestionsDeepLink())
                } else {
                    navByDeepLink(
                        AskDeepLink(
                            GapoDeepLink.Options(
                                bundleOf(
                                    AskDeepLink.ANSWER_USER_ID_EXTRA to it.userId,
                                    AskDeepLink.ANSWER_USER_DISPLAY_NAME_EXTRA to it.userDisplayName
                                )
                            )
                        )
                    )
                }
            }
        )

        userSavedPostViewModel.onPostReactClickOnOpenReactedUsers.observe(
            viewLifecycleOwner,
            EventObserver {
                val post = userSavedPostViewModel.getPost(it.postId) ?: return@EventObserver
                navByDeepLink(
                    PostReactedUsersDeepLink(
                        PostReactedUsersDeepLink.POST_CONTEXT,
                        it.postId,
                        GapoDeepLink.Options(
                            bundle = bundleOf(
                                PostReactedUsersDeepLink.REACT_COUNT_EXTRA to post.reactCount
                            )
                        )
                    )
                )
            }
        )

        userSavedPostViewModel.onPostPollVoteClickOnAddVote.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(PostPollAddVoteDeepLink(it))
            }
        )

        userSavedPostViewModel.onPostPollVoteClickOnVotedUsers.observe(
            viewLifecycleOwner,
            EventObserver {
                val postModel = userSavedPostViewModel.getPostOrFirstSharedPost(it.postId)
                    ?: return@EventObserver
                val voteModel = postModel.pollVote?.votes?.find { vote -> vote.id == it.voteId }
                    ?: return@EventObserver
                navByDeepLink(
                    PostPollVotedUsersDeepLink(
                        it.postId,
                        it.voteId,
                        GapoDeepLink.Options(
                            PostPollVotedUsersDeepLink.createBundle(voteModel.count)
                        )
                    )
                )
            }
        )

        userSavedPostViewModel.onPostMediaVideoWatchedLength.observe(
            viewLifecycleOwner,
            EventObserver {
                val shouldTrackLiveStream =
                    userSavedPostViewModel.getPostOrFirstSharedPost(it.postId)?.shouldTrackLiveStream
                        ?: false

                GAPOAnalytics.getInstance(requireContext()).logEventFinishVideo(
                    id = it.postId,
                    watchLength = it.watchedLength,
                    totalDuration = it.duration,
                    isSteam = shouldTrackLiveStream,
                    videoId = it.mediaId,
                    screenName = GAPOAnalyticsEvents.SCREEN_FOLLOWING
                )
            }
        )

        userSavedPostViewModel.onPostReactClickOnComment.observe(
            viewLifecycleOwner,
            EventObserver {
                requireContext().navToPostDetailsActivity(it.postId)
            }
        )

        userSavedPostViewModel.onPostMediaClickOnMedia.observe(
            viewLifecycleOwner,
            EventObserver {
                val parentPostId = it.parentPostId
                val postModel = if (parentPostId.isNullOrEmpty()) {
                    userSavedPostViewModel.getPost(it.postId)
                } else {
                    userSavedPostViewModel.getSharedPost(parentPostId, it.postId)
                } ?: return@EventObserver
                if (postModel.isSingleMedia) {
                    navByDeepLink(
                        PostMediaDetailsHorizontalDeepLink(
                            it.postId,
                            it.mediaId
                        )
                    )
                } else {
                    navByDeepLink(PostMediaDetailsVerticalDeepLink(it.postId, it.mediaId))
                }
            }
        )

        userSavedPostViewModel.onPostCommentClickOnSticker.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(StickerDetailsSheetDeepLink(stickerId = it.stickerId))
            }
        )

        userSavedPostViewModel.onPostHeaderClickOnMoreTaggedUsers.observe(
            viewLifecycleOwner,
            EventObserver {
                val postModel =
                    userSavedPostViewModel.getPostOrFirstSharedPost(it) ?: return@EventObserver
                navByDeepLink(
                    PostTaggedUsersDeepLink(
                        GapoDeepLink.Options(
                            bundle = bundleOf(
                                PostTaggedUsersDeepLink.USERS_EXTRA to ArrayList(
                                    postModel.taggedUsers
                                )
                            )
                        )
                    )
                )
            }
        )

        userSavedPostViewModel.onPostHeaderClickOnMoreAchievementUsers
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    val postModel =
                        userSavedPostViewModel.getPostOrFirstSharedPost(it) ?: return@EventObserver
                    navByDeepLink(
                        PostTaggedUsersDeepLink(
                            GapoDeepLink.Options(
                                bundle = bundleOf(
                                    PostTaggedUsersDeepLink.USERS_EXTRA to ArrayList(
                                        postModel.acknowledge?.targetUsers.orEmpty()
                                    )
                                )
                            )
                        )
                    )
                }
            )

        userSavedPostViewModel.onPostCommentClickOnMedia.observe(
            viewLifecycleOwner,
            EventObserver {
                navByDeepLink(
                    MediaViewerDeepLink(
                        GapoDeepLink.Options(
                            Bundle().apply {
                                putHeavyObjects(
                                    requireContext(),
                                    MediaViewerDeepLink.MEDIA_VIEWER_MEDIA_DATA_KEY,
                                    listOf(
                                        MediaViewerDeepLink.Media.createSingleMedia(
                                            it.src,
                                            it.thumb,
                                            it.thumb
                                        )
                                    )
                                )
                                putInt(MediaViewerDeepLink.START_POSITION_EXTRA, 0)
                            }
                        )
                    )
                )
            }
        )

        userSavedPostViewModel.onPostClickOnView.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().navToPostDetailsActivity(it)
            }
        )

        userSavedPostViewModel.onPostCommentClickOnView.observe(
            viewLifecycleOwner,
            EventObserver {
                requireActivity().navToPostDetailsActivity(it.postId, it.commentId)
            }
        )

        userSavedPostViewModel.onPostLetAskMeClickOnAsk.observe(
            viewLifecycleOwner,
            EventObserver {
                if (it.userId == userSavedPostViewModel.myUserId) {
                    navByDeepLink(MyQuestionsDeepLink())
                } else {
                    navByDeepLink(
                        AskDeepLink(
                            GapoDeepLink.Options(
                                bundleOf(
                                    AskDeepLink.ANSWER_USER_ID_EXTRA to it.userId,
                                    AskDeepLink.ANSWER_USER_DISPLAY_NAME_EXTRA to it.userDisplayName
                                )
                            )
                        )
                    )
                }
            }
        )

        userSavedPostViewModel.onPostMediaVideoWatchedLength.observe(
            viewLifecycleOwner,
            EventObserver {
                val shouldTrackLiveStream =
                    userSavedPostViewModel.getPostOrFirstSharedPost(it.postId)?.shouldTrackLiveStream
                        ?: false

                GAPOAnalytics.getInstance(requireContext()).logEventFinishVideo(
                    id = it.postId,
                    watchLength = it.watchedLength,
                    totalDuration = it.duration,
                    isSteam = shouldTrackLiveStream,
                    videoId = it.mediaId,
                    screenName = GAPOAnalyticsEvents.SCREEN_FOLLOWING
                )
            }
        )

        userSavedPostViewModel.onPostHeaderSharePostClickOnGroup.observe(
            viewLifecycleOwner,
            EventObserver { event ->
                navByDeepLink(
                    PostShareDeepLink(
                        GapoDeepLink.Options(
                            bundle = PostShareDeepLink.createGroupBundle(
                                data = event.groupList.map { it as PostGroupModel },
                                postId = event.postId,
                                isView = true,
                                defaultId = null,
                                targetId = event.targetId
                            )
                        )
                    )
                )
            }
        )
    }

    private fun initView() {
        binding.swipeRefreshLayout.setOnRefreshListener(this)
        binding.buttonBack.setDebouncedClickListener {
            activity?.onBackPressedDispatcher?.onBackPressed()
        }
    }

    private fun initRcvFeed() {
        val feedSharedVideoPlayerProvider =
            requireContext().applicationContext as? FeedSharedVideoPlayerProvider
        require(feedSharedVideoPlayerProvider != null)
        feedVideoPlayer = feedSharedVideoPlayerProvider.sharedFeedVideoPlayer

        val feedSharedRecyclerViewPoolProvider =
            requireActivity() as? FeedSharedRecyclerViewPoolProvider

        val feedRecyclerViewPool: NoLimitRecycledViewPool
        val feedNestedRecyclerViewViewPool: NoLimitRecycledViewPool

        if (feedSharedRecyclerViewPoolProvider == null) {
            feedRecyclerViewPool = NoLimitRecycledViewPool()
            feedNestedRecyclerViewViewPool = NoLimitRecycledViewPool()
            feedImageLoader = GapoGlide.with(this)
        } else {
            feedRecyclerViewPool = feedSharedRecyclerViewPoolProvider.feedPostRecyclerViewPool
            feedNestedRecyclerViewViewPool =
                feedSharedRecyclerViewPoolProvider.feedNestedRecyclerViewViewPool
            val sharedImageLoaderProvider = requireActivity() as? FeedSharedImageLoaderProvider
            require(sharedImageLoaderProvider != null)

            feedImageLoader = sharedImageLoaderProvider.sharedFeedImageLoader
        }

        feedAdapter = FeedAdapter(
            requireContext(),
            feedNestedRecyclerViewViewPool,
            feedImageLoader,
            feedVideoPlayer
        ).also { it.registerVideoPlayerManager(this, binding.listSavedPost, MemoryMode.NORMAL) }

        binding.listSavedPost.apply {
            setRecycledViewPool(feedRecyclerViewPool)

            setOnFeedFetchMoreDataListener(object :
                    FeedRecyclerView.OnFeedFetchMoreDataListener {
                    override val isFeedDataFetching: Boolean
                        get() = userSavedPostViewModel.isUserSavedPostFetching

                    override fun onFetchMoreFeedData() {
                        userSavedPostViewModel.fetchMoreUserSavedPost()
                    }
                })

            adapter = feedAdapter
        }
    }

    override fun onRefresh() {
        userSavedPostViewModel.fetchData()
    }

    private fun navToUserProfile(userId: String) {
        navByDeepLink(
            UserProfileDeepLink(
                userId
            )
        )
    }

    private fun navToPostDetails(
        postId: String,
        commentId: String? = null,
        isKeyboardShown: Boolean = false
    ) {
        navByDeepLink(
            PostDetailsDeepLink(
                postId,
                commentId.orEmpty(),
                GapoDeepLink.Options(bundle = bundleOf(PostDetailsDeepLink.IS_KEYBOARD_SHOWN_EXTRA to isKeyboardShown))
            )
        )
    }

    private fun onDeniedPermission() {
        GapoToast.makeNegative(
            requireContext(),
            GapoStrings.shared_permission_denied_msg,
            Toast.LENGTH_SHORT
        ).show()
    }

    companion object {
        private val DOWNLOAD_ATTACHMENT_PERMISSION = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO
        ) else arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
    }
}
