package com.gg.gapo.feature.livestream.presentation.viewer.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.gg.gapo.core.ui.bottomsheet.GapoExpandedBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.feature.livestream.databinding.LsBottomSheetStreamerInfoBinding
import com.gg.gapo.feature.livestream.presentation.base.ShareLivestreamViewModel
import org.koin.androidx.viewmodel.ext.android.activityViewModel
import timber.log.Timber
import htkien.autodimens.R.dimen as GPDimens

internal class StreamerInfoBottomSheet : GapoExpandedBottomSheetFragment() {

    private var binding by autoCleared<LsBottomSheetStreamerInfoBinding> {
        try {
            it.imageAvatar.clear(requestManager!!)
        } catch (e: Exception) {
        }
        requestManager = null
    }

    private var requestManager: GlideRequests? = null

    private val viewModel by activityViewModel<StreamerInfoViewModel>()

    private val sharedViewModel by activityViewModel<ShareLivestreamViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LsBottomSheetStreamerInfoBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        viewModel.getStreamerInfo(sharedViewModel.post!!.user!!.id!!)
        return binding.root
    }

    /**
     * Called immediately after [.onCreateView]
     * has returned, but before any saved state has been restored in to the view.
     * This gives subclasses a chance to initialize themselves once
     * they know their view hierarchy has been completely created.  The fragment's
     * view hierarchy is not however attached to its parent at this point.
     * @param view The View returned by [.onCreateView].
     * @param savedInstanceState If non-null, this fragment is being re-constructed
     * from a previous saved state as given here.
     */
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestManager = GapoGlide.with(this)
        observeEvent()
    }

    private fun observeEvent() {
        viewModel.forceQuitEvent.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let {
                if (it) dismiss()
            }
        }
        viewModel.requestPostEvent.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let {
                if (it) {
                    binding.imageAvatar.loadCircle(
                        requestManager!!,
                        avatarUrl = viewModel.avatarUrl.value.orEmpty(),
                        userDisplayName = viewModel.name.value.orEmpty(),
                        placeholderSizeRes = GPDimens._64dp
                    )
                    viewModel.getPostData(sharedViewModel.post!!)
                }
            }
        }
        viewModel.getPostResponseModel.observe(viewLifecycleOwner) {
            Timber.d(this.javaClass.simpleName, "getPostResponseModel")
        }
        viewModel.contentVisibility.observe(viewLifecycleOwner) {
            if (it == View.VISIBLE) {
                try {
                    this.requireView().requestLayout()
                } catch (e: Exception) {
                    Timber.d(e.message)
                }
            }
        }
    }

    companion object {

        const val EXTRA_REQUEST_KEY = "LiveStreamDiscardBottomSheet"

        fun newInstance() = StreamerInfoBottomSheet()
    }
}
