package com.gg.gapo.feature.livestream.presentation.viewer

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.graphics.Color
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.PRIORITY_MIN
import com.gg.gapo.core.ui.GapoAttributes
import com.gg.gapo.core.utilities.di.qualifier.GapoConstantQualifier
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import org.koin.android.ext.android.inject
import org.koin.core.qualifier.named

/*
**
* ViewerLiveStreamService
*
* Main purpose: Kill PIP if user kill app
*/
internal class ViewerLiveStreamService : Service() {

    private val appNotificationIconResId by inject<Int>(qualifier = named(GapoConstantQualifier.APP_NOTIFICATION_ICON_RES_ID))

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val title = intent?.getStringExtra(TITLE_FOREGROUND_VIEWER_LIVESTREAM_SERVICE) ?: ""
        val channelId =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel("ViewerLiveStreamService", "ViewerLiveStreamService")
            } else {
                // If earlier version channel ID is not used
                // https://developer.android.com/reference/android/support/v4/app/NotificationCompat.Builder.html#NotificationCompat.Builder(android.content.Context)
                "ViewerLiveStreamService"
            }
        val notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle(title)
            .setColor(GapoGlobalResources.getColor(this, GapoAttributes.accentWorkPrimary))
            .setContentIntent(
                PendingIntent.getActivity(
                    this,
                    0,
                    Intent(this, ViewerLiveStreamActivity::class.java),
                    0
                )
            )
            .setSmallIcon(appNotificationIconResId)
            .setPriority(PRIORITY_MIN)
            .setCategory(Notification.CATEGORY_SERVICE).build()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            startForeground(100, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE)
        } else {
            startForeground(100, notification)
        }

        return START_NOT_STICKY
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(channelId: String, channelName: String): String {
        val chan = NotificationChannel(
            channelId,
            channelName,
            NotificationManager.IMPORTANCE_NONE
        )
        chan.lightColor = Color.BLUE
        chan.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val service = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        service.createNotificationChannel(chan)
        return channelId
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        PendingIntent.getActivity(this, 0, Intent(this, ViewerLiveStreamActivity::class.java), PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT)
            .cancel()
        super.onTaskRemoved(rootIntent)
        stopSelf()
    }

    companion object {
        const val TITLE_FOREGROUND_VIEWER_LIVESTREAM_SERVICE =
            "TITLE_FOREGROUND_VIEWER_LIVESTREAM_SERVICE"
    }
}
