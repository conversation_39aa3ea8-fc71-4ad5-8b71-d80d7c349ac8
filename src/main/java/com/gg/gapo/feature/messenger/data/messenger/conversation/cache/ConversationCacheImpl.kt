package com.gg.gapo.feature.messenger.data.messenger.conversation.cache

import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.*
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.dto.ConversationDto
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.dto.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.response.TimeStampUserOnline
import com.gg.gapo.feature.messenger.data.messenger.folder.FolderRepository
import com.gg.gapo.feature.messenger.data.messenger.folder.cache.model.FolderEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.*
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Maker
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Receiver
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Settings
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmDispatcher
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmProvider
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.*
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderType
import com.gg.gapo.feature.messenger.domain.messenger.model.mqtt.MqttEventType
import com.gg.gapo.feature.messenger.utils.MessengerConstant
import io.realm.RealmAny
import io.realm.RealmResults
import io.realm.Sort
import io.realm.kotlin.executeTransactionAwait
import io.realm.kotlin.toFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 07/06/2022
 */
internal class ConversationCacheImpl(
    private val realmProvider: RealmProvider,
    private val folderRepository: FolderRepository,
    private val realmDispatcher: RealmDispatcher
) : ConversationCache {

    override suspend fun findBy(conversationId: Long): ConversationEntity? =
        realmProvider.retrieveRealm().where(ConversationEntity::class.java).equalTo(
            ConversationEntity.ID,
            conversationId
        ).findFirst()

    override suspend fun findSubThreadById(conversationId: Long): SubThreadEntity? =
        realmProvider.retrieveRealm().where(SubThreadEntity::class.java).equalTo(
            SubThreadEntity.ID,
            conversationId
        ).findFirst()

    override suspend fun findByFolderType(folderType: FolderType): RealmResults<ConversationEntity> {
        return when (folderType) {
            FolderType.Default -> realmProvider.retrieveRealm()
                .where(ConversationEntity::class.java)
                .notEqualTo(ConversationEntity.FOLDER_ALIAS, FolderType.Stranger.alias)
                .notEqualTo(ConversationEntity.TYPE, ConversationType.SUB_THREAD.type)
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .findAll()

            FolderType.Unread -> realmProvider.retrieveRealm().where(ConversationEntity::class.java)
                .notEqualTo(ConversationEntity.FOLDER_ALIAS, FolderType.Stranger.alias)
                .notEqualTo(ConversationEntity.TYPE, ConversationType.SUB_THREAD.type)
                .notEqualTo(ConversationEntity.UN_READ_COUNT, 0.toInt())
                .or()
                .equalTo(ConversationEntity.MARK_UNREAD, true)
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .findAll()

            else -> realmProvider.retrieveRealm().where(ConversationEntity::class.java)
                .equalTo(ConversationEntity.FOLDER_ALIAS, folderType.alias)
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .findAll()
        }
    }

    override suspend fun findSubthreadsByParentId(
        parentConversationId: Long,
        joined: Boolean
    ): RealmResults<ConversationEntity> {
        return realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .equalTo(ConversationEntity.PARENT_ID, parentConversationId)
            .equalTo(ConversationEntity.TYPE, ConversationType.SUB_THREAD.type)
            .or()
            .equalTo(ConversationEntity.FOLDER_ALIAS, FolderType.SubThread.alias)
            .apply {
                if (!joined) {
                    equalTo(ConversationEntity.ROLE, ConversationRole.GUEST.role)
                } else {
                    notEqualTo(ConversationEntity.ROLE, ConversationRole.GUEST.role)
                }
            }.sort(
                ConversationEntity.SUBTHREAD_UPDATED,
                Sort.DESCENDING
            ).findAll()
    }

    override suspend fun findByPartnerIsUserInFolder(folderType: FolderType): RealmResults<ConversationEntity> {
        return when (folderType) {
            FolderType.Default -> realmProvider.retrieveRealm()
                .where(ConversationEntity::class.java)
                .equalTo(ConversationEntity.PARTNER_TYPE, UserType.USER.type)
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .findAll()

            FolderType.Unread -> realmProvider.retrieveRealm().where(ConversationEntity::class.java)
                .notEqualTo(ConversationEntity.UN_READ_COUNT, 0.toInt())
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .equalTo(ConversationEntity.PARTNER_TYPE, UserType.USER.type)
                .findAll()

            else -> realmProvider.retrieveRealm().where(ConversationEntity::class.java)
                .equalTo(ConversationEntity.FOLDER_ALIAS, folderType.alias)
                .sort(
                    ConversationEntity.PINNED_AT,
                    Sort.DESCENDING,
                    ConversationEntity.LAST_MESSAGE_CREATED_AT,
                    Sort.DESCENDING
                )
                .equalTo(ConversationEntity.PARTNER_TYPE, UserType.USER.type)
                .findAll()
        }
    }

    override suspend fun save(conversation: ConversationEntity) =
        withContext(realmDispatcher) {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                it.copyToRealmOrUpdate(conversation)
            }
        }

    override suspend fun save(subThreadEntity: SubThreadEntity) =
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
            it.copyToRealmOrUpdate(subThreadEntity)
        }

    override suspend fun save(conversations: List<ConversationEntity>) =
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
            it.copyToRealmOrUpdate(conversations)
        }

    override suspend fun updateAndRemoveAllByFolderType(
        folderType: FolderType,
        conversationsEntity: List<ConversationEntity>,
        lastMessages: List<MessageEntity>,
        isForce: Boolean,
        referencedMessages: List<MessageEntity>
    ) {
        val realm = realmProvider.retrieveRealm()
        val conversationsCached = buildList {
            conversationsEntity.forEach { newConversationEntity ->
                val conversationCached =
                    realm.where(ConversationEntity::class.java)
                        .equalTo(ConversationEntity.ID, newConversationEntity.id).findFirst()
                add(conversationCached)
            }
        }

        val lastMessagesCached = buildList {
            conversationsEntity.forEachIndexed { index, newConversationEntity ->
                val newLastMessage = lastMessages.getOrNull(index)
                val lastMessageCached = realm.where(MessageEntity::class.java)
                    .equalTo(
                        MessageEntity.UID,
                        "${newConversationEntity.id}_${newLastMessage?.id ?: 0}"
                    ).findFirst()
                add(lastMessageCached)
            }
        }

        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            conversationsCached.forEachIndexed { index, conversationCached ->
                val lastMessageCached = lastMessagesCached.getOrNull(index)

                val newConversationEntity = conversationsEntity.getOrNull(index)
                val newLastMessage = lastMessages.getOrNull(index)
                newConversationEntity?.let {
                    if (conversationCached == null) { // chua co trong db
                        realm.copyToRealmOrUpdate(newConversationEntity)
                        newLastMessage?.let {
                            realm.copyToRealmOrUpdate(it)
                        }
                    } else { // da co trong db
                        conversationCached.update(newConversationEntity, realm)

                        newLastMessage?.let {
                            if (lastMessageCached != null) { // da ton tai message
                                val lastMessageModel = lastMessageCached.mapToDomain()
                                lastMessageCached.updateBody(
                                    lastMessageModel.body,
                                    newConversationEntity.id,
                                    lastMessageCached.uid,
                                    realm,
                                    lastMessageModel
                                )
                            } else {
                                newLastMessage.conversation = conversationCached
                                realm.copyToRealmOrUpdate(newLastMessage)
                            }
                        }
                    }

                    // save referencedMessages
                    realm.copyToRealmOrUpdate(referencedMessages)
                }
            }
        }

        if (isForce) { // delete conversation con lai trong folder
            val currentConversationsCached = findByFolderType(folderType)
            val newConversationIds = conversationsEntity.map { it.id }
            val conversationsCachedToDelete = buildList {
                currentConversationsCached.forEach { conversationCached ->
                    if (conversationCached != null && !newConversationIds.contains(
                            conversationCached.id
                        )
                    ) {
                        add(conversationCached)
                    }
                }
            }

            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                conversationsCachedToDelete.forEach {
                    it.deleteFromRealm()
                }
            }
        }
    }

    override suspend fun updateAndRemoveAllSubthreads(
        joined: Boolean,
        parentConversationId: Long,
        conversationsEntity: List<ConversationEntity>,
        lastMessages: List<MessageEntity>,
        isForce: Boolean,
        referencedMessages: List<MessageEntity>
    ) {
        val realm = realmProvider.retrieveRealm()
        val conversationsCached = buildList {
            conversationsEntity.forEach { newConversationEntity ->
                val conversationCached =
                    realm.where(ConversationEntity::class.java)
                        .equalTo(ConversationEntity.ID, newConversationEntity.id).findFirst()
                add(conversationCached)
            }
        }

        val lastMessagesCached = buildList {
            conversationsEntity.forEachIndexed { index, newConversationEntity ->
                val newLastMessage = lastMessages.getOrNull(index)
                val lastMessageCached = realm.where(MessageEntity::class.java)
                    .equalTo(
                        MessageEntity.UID,
                        "${newConversationEntity.id}_${newLastMessage?.id ?: 0}"
                    ).findFirst()
                add(lastMessageCached)
            }
        }

        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            conversationsCached.forEachIndexed { index, conversationCached ->
                val lastMessageCached = lastMessagesCached.getOrNull(index)

                val newConversationEntity = conversationsEntity.getOrNull(index)
                val newLastMessage = lastMessages.getOrNull(index)
                newConversationEntity?.let {
                    if (conversationCached == null) { // chua co trong db
                        realm.copyToRealmOrUpdate(newConversationEntity)
                        newLastMessage?.let {
                            realm.copyToRealmOrUpdate(it)
                        }
                    } else { // da co trong db
                        conversationCached.update(newConversationEntity, realm)

                        newLastMessage?.let {
                            if (lastMessageCached != null) { // da ton tai message
                                val lastMessageModel = lastMessageCached.mapToDomain()
                                lastMessageCached.updateBody(
                                    lastMessageModel.body,
                                    newConversationEntity.id,
                                    lastMessageCached.uid,
                                    realm,
                                    lastMessageModel
                                )
                            } else {
                                newLastMessage.conversation = conversationCached
                                realm.copyToRealmOrUpdate(newLastMessage)
                            }
                        }
                    }

                    // save referencedMessages
                    realm.copyToRealmOrUpdate(referencedMessages)
                }
            }
        }

        if (isForce) { // delete conversation con lai trong folder
            val currentConversationsCached = findSubthreadsByParentId(parentConversationId, joined)
            val newConversationIds = conversationsEntity.map { it.id }
            val conversationsCachedToDelete = buildList {
                currentConversationsCached.forEach { conversationCached ->
                    if (conversationCached != null && !newConversationIds.contains(
                            conversationCached.id
                        )
                    ) {
                        add(conversationCached)
                    }
                }
            }

            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                conversationsCachedToDelete.forEach {
                    it.deleteFromRealm()
                }
            }
        }
    }

    override suspend fun savePageConversation(
        conversations: List<ConversationEntity>,
        lastMessages: List<MessageEntity>,
        referencedMessages: List<MessageEntity>
    ) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(conversations)
            realm.copyToRealmOrUpdate(lastMessages)
            realm.copyToRealmOrUpdate(referencedMessages)
        }
    }

    override suspend fun update(conversation: ConversationEntity) {
        val conversationCached = findBy(conversation.id)

        if (conversationCached == null) { // chua co trong db
            val folderCached =
                realmProvider.retrieveRealm().where(FolderEntity::class.java)
                    .equalTo(FolderEntity.ALIAS, conversation.folderAlias)
                    .findFirst()
            conversation.folder = folderCached
            save(conversation)
        } else { // da co trong db
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                conversationCached.updateFetchConversation(conversation, realm)
            }
        }
    }

    override suspend fun removeById(conversationId: Long) {
        try {
            val conversation =
                realmProvider.retrieveRealm().where(ConversationEntity::class.java)
                    .equalTo(ConversationEntity.ID, conversationId)
                    .findFirst()
            val messages =
                realmProvider.retrieveRealm().where(MessageEntity::class.java)
                    .contains(MessageEntity.UID, "${conversationId}_").findAll()
            val conversationLastVisit =
                realmProvider.retrieveRealm().where(ConversationLastVisitEntity::class.java)
                    .equalTo(ConversationLastVisitEntity.ID, conversationId)
                    .findFirst()
            conversation?.let {
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                    conversationLastVisit?.deleteFromRealm()
                    messages.deleteAllFromRealm()
                    conversation.deleteFromRealm()
                }
            }
        } catch (e: Exception) {
            // Timber.e(e)
        }
    }

    override suspend fun updateTimeStampUserOnline(listTimeStampUserOnline: List<TimeStampUserOnline>) =
        try {
            val realm = realmProvider.retrieveRealm()
            val partnersCached = buildList {
                listTimeStampUserOnline.forEach { timeStampUserOnline ->
                    if (timeStampUserOnline.id != null) {
                        val partnerCached =
                            realm.where(PartnerEntity::class.java)
                                .equalTo(PartnerEntity.ID, timeStampUserOnline.id).findFirst()
                        add(partnerCached)
                    } else add(null)
                }
            }
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                listTimeStampUserOnline.forEachIndexed { index, timeStampUserOnline ->
                    val partnerCached = partnersCached.getOrNull(index)
                    partnerCached?.let {
                        partnerCached.timeStampOnline = timeStampUserOnline.seenAt ?: 0L
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            throw e
        }

    // TODO conversationCached can cho update vao entity
    override suspend fun createConversationMqtt(
        conversationEntity: ConversationEntity,
        messageEntity: MessageEntity?,
        isMe: Boolean
    ): ConversationEntity {
        // nếu conversation chua co thi tao moi, khong thi can update 1 so field
        // can update unreadcount cho folder

        // if isSaveMessage luu tru thi unreadcount = 0
        val isSaveMessage = messageEntity?.isSaveMessageForward ?: false

        // TODO SUBTHREAD: cần refactor lại ra hàm riêng cho subthread
        if (conversationEntity.type == "subthread") {
            val subthreadCached = findSubThreadById(conversationEntity.id)

            if (subthreadCached == null) {
                val subthread = conversationEntity.mapToSubthreadEntity(
                    realmProvider.retrieveRealm(),
                    messageEntity?.sender?.id
                )
                val message =
                    realmProvider.retrieveRealm().where(MessageEntity::class.java).equalTo(
                        MessageEntity.UID,
                        "${conversationEntity.parentId ?: 0}_${conversationEntity.rootMessageId}"
                    ).findFirst()
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                    val subthreadEntity = it.copyToRealmOrUpdate(subthread)
                    message?.subThreadId = conversationEntity.id
                    message?.subThread = subthreadEntity
                }
            } else {
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                    subthreadCached.messageCount = conversationEntity.messageCount
                }
            }
        }

        var conversationCached = findBy(conversationEntity.id)
        if (conversationCached == null) {
            conversationEntity.apply {
                unReadCount = if (isSaveMessage) 0 else 1
            }
            conversationCached = conversationEntity

            if (!messageEntity?.nameActionNoteDataChangedMetadataBody.isNullOrEmpty()) {
                conversationCached.name = messageEntity?.nameActionNoteDataChangedMetadataBody
            }
            if (!messageEntity?.avatarActionNoteDataChangedMetadataBody.isNullOrEmpty()) {
                conversationCached.avatar =
                    messageEntity?.avatarActionNoteDataChangedMetadataBody
            }

            save(conversationCached)

            folderRepository.fetchFolders()
        } else {
            val currentUnReadCount = conversationCached.unReadCount ?: 0
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                conversationCached.messageCount = conversationEntity.messageCount
                if (isMe) {
                    conversationCached.unReadCount = 0
                } else {
                    conversationCached.unReadCount =
                        if (isSaveMessage) 0 else (currentUnReadCount + 1)

                    // TODO list tags k đúng tuyệt đối, improve sau
                    if (!conversationEntity.tags.isNullOrEmpty()) conversationCached.tags =
                        conversationEntity.tags
                }
                conversationCached.lastMessageCreatedAt =
                    conversationEntity.lastMessageCreatedAt

                if (!messageEntity?.nameActionNoteDataChangedMetadataBody.isNullOrEmpty()) {
                    conversationCached.name =
                        messageEntity?.nameActionNoteDataChangedMetadataBody
                }
                if (!messageEntity?.avatarActionNoteDataChangedMetadataBody.isNullOrEmpty()) {
                    conversationCached.avatar =
                        messageEntity?.avatarActionNoteDataChangedMetadataBody
                }
                conversationCached.subthreadUpdated = messageEntity?.createdAt

                // conversationCached.folder = conversationEntity.folder
            }

            val unReadCount = conversationCached.unReadCount
            if (currentUnReadCount != unReadCount && (currentUnReadCount == 0 || unReadCount == 0)) {
                val isPlusCount = currentUnReadCount == 0
                folderRepository.updateUnReadCountFolderCache(
                    FolderType.Unread.alias,
                    isPlusCount
                )
                conversationCached.folder?.alias?.let { alias ->
                    if (FolderType.getByType(alias) != FolderType.Default && FolderType.getByType(
                            alias
                        ) != FolderType.Unread
                    ) {
                        folderRepository.updateUnReadCountFolderCache(alias, isPlusCount)
                    }
                }
            }
        }
        return conversationCached
    }

    override suspend fun readAtConversationMqtt(
        conversationId: Long,
        messageId: Int,
        userId: String
    ) {
        val conversationCached = findBy(conversationId)
        val lastMessageId = conversationCached?.messages?.max(MessageEntity.ID)?.toInt()

        if (conversationCached != null && lastMessageId != null) {
            val currentUnReadCount = conversationCached.unReadCount
            val unReadCount = if (lastMessageId >= messageId) (lastMessageId - messageId) else 0

            if (currentUnReadCount != null && currentUnReadCount != unReadCount && unReadCount < currentUnReadCount) {
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                    conversationCached.unReadCount = unReadCount
                    conversationCached.tags = "" // TODO k chinh xac tuyet doi, improve sau
                    conversationCached.markUnread = false
                }

                // update unread_count cho folder
                if (unReadCount == 0 && currentUnReadCount > 0) {
                    folderRepository.updateUnReadCountFolderCache(FolderType.Unread.alias, false)
                    conversationCached.folder?.alias?.let { alias ->
                        if (FolderType.getByType(alias) != FolderType.Default && FolderType.getByType(
                                alias
                            ) != FolderType.Unread
                        ) {
                            folderRepository.updateUnReadCountFolderCache(alias, false)
                        }
                    }
                }
            }
        } else {
            folderRepository.fetchFolders()
        }
    }

    override suspend fun pinConversationCache(
        folder: FolderType,
        conversationId: Long,
        pinnedAt: Long
    ): Long {
        val conversationCached = findBy(conversationId)
        val currentPinnedAt = conversationCached?.pinnedAt
        val pinnedEntity = PinnedEntity(folder.alias, conversationId, pinnedAt)
        // update lai pinned_at
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(pinnedEntity)
            conversationCached?.pinnedAt = pinnedAt
        }
        return currentPinnedAt ?: 0
    }

    override suspend fun toggleNotifyConversationCache(conversationId: Long) {
        val conversationCache = findBy(conversationId)
        val enableNotify =
            ConversationEnableNotify.getByEnableNotify(conversationCache?.enableNotify)
        conversationCache?.realm?.executeTransactionAwait(realmDispatcher) {
            when (enableNotify) {
                ConversationEnableNotify.GONE ->
                    conversationCache.enableNotify =
                        ConversationEnableNotify.VISIBLE.enableNotify
                ConversationEnableNotify.VISIBLE ->
                    conversationCache.enableNotify =
                        ConversationEnableNotify.GONE.enableNotify
                else -> {}
            }
        }
    }

    override suspend fun toggleNotifySubthreadCache(subthreadId: Long) {
        val conversationCache = findBy(subthreadId)
        val enableNotify =
            ConversationEnableNotify.getByEnableNotify(conversationCache?.enableNotify)
        conversationCache?.realm?.executeTransactionAwait(realmDispatcher) {
            when (enableNotify) {
                ConversationEnableNotify.GONE ->
                    conversationCache.enableNotify =
                        ConversationEnableNotify.VISIBLE.enableNotify
                ConversationEnableNotify.VISIBLE ->
                    conversationCache.enableNotify =
                        ConversationEnableNotify.GONE.enableNotify
                else -> {}
            }
            conversationCache.role = ConversationRole.MEMBER.role
        }
    }

    override fun conversationFlow(conversationId: Long): Flow<ConversationModel?> =
        flow { emit(realmProvider.retrieveRealm()) }
            .flatMapLatest { realm ->
                realm.where(ConversationEntity::class.java)
                    .equalTo(ConversationEntity.ID, conversationId).findFirst().toFlow().map {
                        it?.mapToDomain()
                    }
            }

    override suspend fun getAllConversationCachingOnlyMessage(folderType: FolderType): List<ConversationEntity> {
        val allConversationCached = findByFolderType(folderType)
        // TODO con TH lastMessageId - preMessageId > 25
        return buildList {
            allConversationCached.forEach {
                if (it.messages.isNullOrEmpty() || it.messages.size == 1) add(it)
            }
        }
    }

    override suspend fun getForwardConversation(): List<ConversationEntity> {
        val result = ArrayList<ConversationEntity>()
        val maxItems: Long = 4
        val lasted = realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .notEqualTo(ConversationEntity.FOLDER_ALIAS, FolderType.SubThread.alias)
            .notEqualTo(ConversationEntity.TYPE, ConversationType.SUB_THREAD.type)
            .greaterThan(ConversationEntity.ID, 0)
            .sort(
                ConversationEntity.LAST_MESSAGE_CREATED_AT,
                Sort.DESCENDING
            )
            .limit(maxItems)
            .findAll()
        val groups = realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .greaterThan(ConversationEntity.ID, 0)
            .equalTo(ConversationEntity.TYPE, ConversationType.GROUP.type)
            .distinct(ConversationEntity.PARENT_ID)
            .apply {
                lasted.forEach {
                    this.notEqualTo(ConversationEntity.ID, it.id)
                }
            }
            .sort(
                ConversationEntity.LAST_MESSAGE_CREATED_AT,
                Sort.DESCENDING
            )
            .limit(maxItems)
            .findAll()

        val settingDisabledMemberSendMessage: Int = 0
        val directs = realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .greaterThan(ConversationEntity.ID, 0)
            .equalTo(ConversationEntity.TYPE, ConversationType.DIRECT.type)
            .equalTo(ConversationEntity.SETTING_DISABLE_MEMBER_SEND_MESSAGE, settingDisabledMemberSendMessage)
            .equalTo(ConversationEntity.BLOCK_BY, RealmAny.nullValue())
            .apply {
                lasted.forEach {
                    this.notEqualTo(ConversationEntity.ID, it.id)
                }
                groups.forEach {
                    this.notEqualTo(ConversationEntity.ID, it.id)
                }
            }
            .sort(
                ConversationEntity.LAST_MESSAGE_CREATED_AT,
                Sort.DESCENDING
            )
            .limit(MessengerConstant.PRELOAD_ITEM.toLong())
            .findAll()
        result.addAll(lasted)
        result.addAll(groups)
        result.addAll(directs)

        return result
    }

    override suspend fun blockConversationMqtt(
        maker: Maker,
        receiver: Receiver,
        block: MqttEventType,
        isMeMaker: Boolean
    ) {
        val partnerId = if (isMeMaker) receiver.id else maker.id
        val conversationCached = realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .equalTo(ConversationEntity.PARTNER_ID, partnerId.orEmpty())
            .findFirst()

        conversationCached?.let {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                if (block == MqttEventType.BLOCK) {
                    conversationCached.blockedBy = maker.id
                } else if (block == MqttEventType.UNBLOCK) {
                    conversationCached.blockedBy = null
                }
            }
        }
    }

    override suspend fun updateSettingsMqtt(conversationId: Long, settings: Settings) {
        val conversationCached = realmProvider.retrieveRealm().where(ConversationEntity::class.java)
            .equalTo(ConversationEntity.ID, conversationId)
            .findFirst()
        val conversationName = conversationCached?.name
        val settingsCached = conversationCached?.settings

        settingsCached?.let {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                settingsCached.isPublic = settings.isPublic ?: BooleanValue.FALSE.value
                settingsCached.disableMemberSendMessage = settings.disableMemberSendMessage ?: BooleanValue.FALSE.value
                settingsCached.disableMemberSendSubMessage = settings.disableMemberSendSubMessage ?: BooleanValue.FALSE.value
                conversationCached.name = conversationName // refresh conversation flow
            }
        }
    }

    override suspend fun getUserParticipantsById(
        conversationId: Long,
        userId: String
    ): MessageUserParticipantsEntity? {
        return realmProvider.retrieveRealm().where(MessageUserParticipantsEntity::class.java)
            .equalTo(MessageUserParticipantsEntity.UID, "${conversationId}_$userId")
            .findFirst()
    }

    override suspend fun deleteUserParticipants(conversationId: Long) {
        val realm = realmProvider.retrieveRealm()
        val usersParticipants =
            realm.where(MessageUserParticipantsEntity::class.java)
                .contains(MessageUserParticipantsEntity.UID, "${conversationId}_")
                .findAll()
        usersParticipants?.let {
            realm.executeTransactionAwait(realmDispatcher) {
                usersParticipants.deleteAllFromRealm()
            }
        }
    }

    override suspend fun saveUserParticipants(usersParticipants: List<MessageUserParticipantsEntity>) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(usersParticipants)
        }
    }

    override suspend fun savePartners(partners: List<PartnerEntity>) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            val userParticipants = realm.where(PartnerEntity::class.java)
                .`in`(PartnerEntity.ID, partners.map { it.id }.toTypedArray())
                .findAll()
            userParticipants.forEach { user ->
                partners.firstOrNull { it.id == user.id }?.let { partner ->
                    user.status = partner.status
                }
            }
        }
    }

    override suspend fun updateAuthorizeMqtt(
        conversationId: Long,
        userId: String,
        role: ConversationRole,
        isMe: Boolean
    ) {
        val realm = realmProvider.retrieveRealm()
        val userParticipants = realm.where(MessageUserParticipantsEntity::class.java)
            .equalTo(MessageUserParticipantsEntity.UID, "${conversationId}_$userId")
            .findFirst()
        if (isMe) {
            val conversationCached = realm.where(ConversationEntity::class.java)
                .equalTo(ConversationEntity.ID, conversationId).findFirst()
            conversationCached?.let {
                realm.executeTransactionAwait(realmDispatcher) {
                    conversationCached.role = role.role
                }
            }
        }
        userParticipants?.let {
            realm.executeTransactionAwait(realmDispatcher) {
                userParticipants.role = role.role
            }
        }
    }

    override suspend fun updateMarkUnReadMqtt(conversationId: Long, markUnread: Boolean) {
        val realm = realmProvider.retrieveRealm()
        val conversationCached = realm.where(ConversationEntity::class.java)
            .equalTo(ConversationEntity.ID, conversationId).findFirst()
        if (conversationCached != null && conversationCached.markUnread != markUnread) {
            realm.executeTransactionAwait(realmDispatcher) {
                conversationCached.markUnread = markUnread
            }
        }
        // folderRepository.updateUnReadCountFolderCache()
        folderRepository.fetchAndUpdateUnReadCountFolder()
    }

    override suspend fun savePinnedAt(pinnedAts: List<PinnedEntity>) =
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
            it.copyToRealmOrUpdate(pinnedAts)
        }

    override suspend fun moveConversationToFolder(conversationId: Long, folderAlias: String) {
        val realm = realmProvider.retrieveRealm()
        val conversationCached = realm.where(ConversationEntity::class.java)
            .equalTo(ConversationEntity.ID, conversationId).findFirst()
        val folderCached =
            realm.where(FolderEntity::class.java).equalTo(FolderEntity.ALIAS, folderAlias)
                .findFirst()
        conversationCached?.let {
            realm.executeTransactionAwait(realmDispatcher) {
                conversationCached.folderAlias = folderAlias
                conversationCached.folder = folderCached
            }
        }
    }

    override suspend fun updateConversationLastVisit(
        conversationId: Long,
        lastVisitAt: Long
    ) {
        val realm = realmProvider.retrieveRealm()
        val conversationLastVisitCached =
            realm.where(ConversationLastVisitEntity::class.java)
                .equalTo(ConversationLastVisitEntity.ID, conversationId).findFirst()
        if (conversationLastVisitCached == null) {
            realm.executeTransactionAwait(realmDispatcher) {
                it.copyToRealmOrUpdate(
                    ConversationLastVisitEntity(
                        conversationId,
                        lastVisitAt
                    )
                )
            }
        } else {
            realm.executeTransactionAwait(realmDispatcher) {
                conversationLastVisitCached.lastVisitAt = lastVisitAt
            }
        }
    }

    override suspend fun getConversationsByVisit(): List<ConversationModel> {
        val realm = realmProvider.retrieveRealm()
        val conversationsLastVisitSaved =
            realm.where(ConversationLastVisitEntity::class.java)
                .sort(ConversationLastVisitEntity.LAST_VISIT_AT, Sort.DESCENDING)
                .findAll()

        val conversationIds =
            realm.copyFromRealm(conversationsLastVisitSaved).map { it.id }.toTypedArray()
        return buildList {
            conversationIds.forEachIndexed { index, conversationId ->
                val conversation = realm.where(ConversationEntity::class.java)
                    .equalTo(ConversationEntity.ID, conversationId).findFirst()
                if (conversation != null) {
                    add(conversation.mapToDomain())
                } else {
                    conversationsLastVisitSaved.getOrNull(index)?.conversationStrangeJson.mapFromJsonToDto<ConversationDto>()
                        ?.mapToDomain()
                        ?.let {
                            add(it)
                        }
                }
            }
        }
    }

    override suspend fun getRemoteKeyForLastItem(): Long? = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().where(RemoteThreadKeys::class.java).min(RemoteThreadKeys.THREAD_ID)
            ?.toLong()
    }

    override suspend fun clearRemoteKeys() = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.delete(RemoteThreadKeys::class.java)
        }
    }

    override suspend fun insertRemoteKeys(remoteKey: RemoteThreadKeys) = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(remoteKey)
        }
    }
}
