package com.gg.gapo.messenger.presentation.features.create

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.eventbus.invite.InvitationCreateChatGroupSuccessBusEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.assignee.picker.AssigneePickerDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupInvitationDeepLink
import com.gg.gapo.core.navigation.deeplink.invitation.InvitationWorkspaceDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.messenger.databinding.FragmentNewMessageBinding
import com.gg.gapo.messenger.helper.utils.CommonUtils
import com.gg.gapo.messenger.presentation.features.create.named.CreateNamedConversationActivity
import com.gg.gapo.messenger.presentation.features.managers.UserChatManager
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.observers.DisposableObserver
import io.reactivex.subjects.PublishSubject
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.concurrent.TimeUnit

class NewMessageFragment : Fragment() {

    private var binding by autoCleared<FragmentNewMessageBinding>()
    private val newMessageViewModel: NewMessageViewModel by viewModel()
    private var page = 0
    private var pastVisibleItems: Int = 0
    private var visibleItemCount: Int = 0
    private var totalItemCount: Int = 0
    private var controller by autoCleared<NewMessageController>()
    private var publishSubject: PublishSubject<String>? = null
    private val userManager: UserChatManager by inject()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentNewMessageBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = newMessageViewModel
        controller = NewMessageController(requireActivity(), userManager.getCurrentUserChatId())
        with(binding.recyclerView) {
            adapter = controller.adapter
            layoutManager = LinearLayoutManager(requireContext()).also {
                it.isItemPrefetchEnabled = true
                it.initialPrefetchItemCount = 4
            }
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    if (dy > 0) {
                        visibleItemCount = layoutManager!!.childCount
                        totalItemCount = layoutManager!!.itemCount
                        pastVisibleItems =
                            (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                        if (visibleItemCount + pastVisibleItems >= totalItemCount && newMessageViewModel.isLoadMore()) {
                            page++
                            newMessageViewModel.getWorkspaceUsers(page)
                        }
                    }
                }
            })
        }
        newMessageViewModel.getWorkspaceUsers(page)
        binding.imvBack.setOnClickListener {
            activity?.finish()
        }
        binding.blockCreateGroup.setOnClickListener {
            newMessageViewModel.clearNetwork()
            binding.searchView.removeTextChangedListener(textWatcher)
            binding.searchView.text?.clear()
            navByDeepLink(
                InvitationWorkspaceDeepLink(
                    options = GapoDeepLink.Options(
                        bundle = Bundle().apply {
                            this.putBoolean(InvitationWorkspaceDeepLink.INCLUDED_BOT_EXTRA, true)
                        }
                    )
                )
            )
        }
        setupSearchView()

        newMessageViewModel.onFriendResult.observe(viewLifecycleOwner) {
            if (page == 0) {
                controller.initData(it)
            } else {
                controller.loadMore(it)
            }
        }
        newMessageViewModel.onSearchResult.observe(viewLifecycleOwner) {
            controller.initData(it)
        }
        registerEventBus()
        return binding.root
    }

    @SuppressLint("CheckResult")
    private fun setupSearchView() {
        binding.searchView.addTextChangedListener(textWatcher)
        if (publishSubject == null) {
            binding.searchView.requestFocus()
            publishSubject = PublishSubject.create()
            publishSubject!!.debounce(600, TimeUnit.MILLISECONDS)
                .distinctUntilChanged()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : DisposableObserver<String>() {
                    override fun onNext(keyword: String) { // Update View here
                        if (CommonUtils.isNullOrEmpty(keyword)) {
                            page = 0
                            newMessageViewModel.isEndList = false
                            newMessageViewModel.getWorkspaceUsers(page)
                            binding.blockCreateGroup.visibility = View.VISIBLE
                        } else {
                            newMessageViewModel.searchUser(keyword)
                            binding.blockCreateGroup.visibility = View.GONE
                        }
                    }

                    override fun onComplete() { // On complete
                    }

                    override fun onError(e: Throwable) {
                    }
                })
        }
    }

    private val textWatcher = object : TextWatcher {

        override fun afterTextChanged(keyword: Editable?) {
            publishSubject?.onNext(keyword.toString())
        }

        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onCreateGroupChatSuccess(event: InvitationCreateChatGroupSuccessBusEvent) {
        activity?.finish()
    }

    override fun onDestroy() {
        unregisterEventBus()
        super.onDestroy()
    }

    companion object {
        const val TAG = "NewMessageFragment"

        @JvmStatic
        fun newInstance() =
            NewMessageFragment()
    }
}
