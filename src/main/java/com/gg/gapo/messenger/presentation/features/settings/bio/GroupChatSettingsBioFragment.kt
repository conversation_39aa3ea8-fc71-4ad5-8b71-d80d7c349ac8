package com.gg.gapo.messenger.presentation.features.settings.bio

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerChatSettingDeepLink
import com.gg.gapo.core.ui.snackbar.makePositiveSnackbar
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.messenger.databinding.FragmentGroupChatSettingsBioBinding
import com.gg.gapo.messenger.helper.extensions.gone
import com.gg.gapo.messenger.helper.extensions.observe
import com.gg.gapo.messenger.helper.utils.CommonUtils
import com.gg.gapo.messenger.helper.utils.ToastUtils
import com.gg.gapo.messenger.presentation.features.settings.group.GroupChatSettingsViewModel
import com.gg.gapo.messenger.presentation.features.settings.group.GroupChatSettingsViewState
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf
import timber.log.Timber
import java.util.concurrent.TimeUnit

class GroupChatSettingsBioFragment : Fragment() {

    private var binding by autoCleared<FragmentGroupChatSettingsBioBinding>()
    private val viewModel by viewModel<GroupChatSettingsViewModel> {
        parametersOf(
            activity?.intent?.extras?.getString(
                MessengerChatSettingDeepLink.CONVERSATION_ID_EXTRA,
                ""
            ).orEmpty()
        )
    }

    val args: GroupChatSettingsBioFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentGroupChatSettingsBioBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.bio.addTextChangedListener {
            binding.countText.text = binding.root.context.getString(com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_settings_privacy_settings_bio_count, it.toString().count())
        }
        binding.groupName.addTextChangedListener {
            binding.countTitleText.text = binding.root.context.getString(com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_settings_privacy_settings_bio_title_count, it.toString().count())
        }
        binding.imvBack.setDebouncedClickListener {
            binding.bio.clearFocus()
            CommonUtils.hideSoftKeyboard(binding.bio)
            findNavController().popBackStack()
        }
        binding.save.setDebouncedClickListener {
            if (binding.groupName.text.isNullOrBlank()) {
                ToastUtils.show(requireContext(), com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_notify_group_name_invalid)
                return@setDebouncedClickListener
            }
            if (binding.bio.text.toString() != args.bio.orEmpty() &&
                binding.groupName.text.toString() != args.groupName.orEmpty()
            ) {
                viewModel.threadUpdateNameAndBio(threadId = args.threadId.toString(), description = binding.bio.text.toString(), name = binding.groupName.text.toString())
            } else {
                if (binding.bio.text.toString() != args.bio.orEmpty()) {
                    viewModel.updateBio(threadId = args.threadId.toString(), description = binding.bio.text.toString())
                }
                if (binding.groupName.text.toString() != args.groupName.orEmpty()) {
                    viewModel.updateName(threadId = args.threadId.toString(), name = binding.groupName.text.toString())
                }
            }
        }

        if (args.bio != null) {
            binding.bio.setText(args.bio.toString())
        }
        if (args.groupName != null) {
            binding.groupName.setText(args.groupName.toString())
        }
        if (args.associateLink.orEmpty().isNotEmpty() && viewModel.isMember(threadId = args.threadId.orEmpty())) {
            binding.groupName.gone()
            binding.groupNameTitle.gone()
            binding.countTitleText.gone()
        }
        viewModel.disposables.add(
            Single.just(true).delay(200, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    activity?.let {
                        CommonUtils.toggleSoftKeyboard(it, binding.bio)
                        binding.bio.requestFocus()
                        if ((binding.bio.text?.length ?: 0) > 0) {
                            binding.bio.setSelection(binding.bio.text!!.length)
                        }
                    }
                }, { Timber.e(it) })
        )
        observe(viewModel.state, ::onViewStateChange)
    }

    private fun onViewStateChange(viewState: GroupChatSettingsViewState) {
        when (viewState) {
            is GroupChatSettingsViewState.onThread -> {
                makePositiveSnackbar(com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_settings_privacy_settings_update_success)?.show()
                binding.imvBack.performClick()
                requireActivity().finish()
            }
            else -> {
            }
        }
    }
}
