package com.gg.gapo.messenger.presentation.features.settings.models

import android.content.Context
import android.view.View
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.ItemMessengerChatSettingsDirectInfoBinding
import com.gg.gapo.messenger.domain.models.Conversation
import com.gg.gapo.messenger.domain.models.EnableNotify
import com.gg.gapo.messenger.domain.models.StatusVerify
import com.gg.gapo.messenger.helper.Constant
import com.gg.gapo.messenger.helper.extensions.gone
import com.gg.gapo.messenger.helper.extensions.load
import com.gg.gapo.messenger.helper.extensions.loadCircleThreadAvatar
import com.gg.gapo.messenger.helper.extensions.setVerified
import com.gg.gapo.messenger.presentation.bases.DataBindingModel
import com.gg.gapo.messenger.presentation.features.settings.listener.SettingOnClickedListener
import org.koin.core.component.KoinComponent

@EpoxyModelClass
abstract class DirectChatSettingsItemInfoModel : DataBindingModel<ItemMessengerChatSettingsDirectInfoBinding>(), KoinComponent {
    @EpoxyAttribute
    var data: Conversation? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onClick: SettingOnClickedListener? = null

    @EpoxyAttribute
    var showSearchMessage: Boolean = true

    override fun bind(binding: ItemMessengerChatSettingsDirectInfoBinding, context: Context) {
        data?.let { conversation ->
            binding.name.setVerified(conversation.partner?.name.orEmpty(), conversation.partner?.statusVerify == StatusVerify.VERIFIED, conversation.partner?.isBot() ?: false)
            binding.avatar.loadCircleThreadAvatar(conversation, GapoAutoDimens._96dp)
            binding.textNotification.setText(
                if (conversation.enableNotify == EnableNotify.ON) {
                    com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_settings_privacy_settings_notification_turn_off
                } else {
                    com.gg.gapo.core.ui.GapoStrings.messenger_group_chat_settings_privacy_settings_notification_turn_on
                }
            )
            binding.iconSecret.visibility = if (conversation.folder == Constant.Folder.SECRET.type) View.VISIBLE else View.GONE
            binding.iconNotification.setImageResource(
                if (conversation.enableNotify == EnableNotify.ON) {
                    R.drawable.ic_chat_settings_notification_on
                } else {
                    R.drawable.ic_chat_settings_notification_off
                }
            )
            if (conversation.status == com.gg.gapo.feature.messenger.domain.messenger.model.conversation.StatusVerify.DE_ACTIVE.statusVerify) {
                binding.imageStatusDeActive.load(R.drawable.messenger_ic_status_de_active)
                binding.imageStatusDeActive.visibility = View.VISIBLE
                binding.cardViewStatusDeActive.visibility = View.VISIBLE
            } else {
                binding.cardViewStatusDeActive.visibility = View.GONE
                binding.imageStatusDeActive.visibility = View.GONE
            }
            binding.avatar.setDebouncedClickListener {
                onClick?.onClickedAvatar()
            }
            binding.name.setDebouncedClickListener {
                onClick?.onClickedName()
            }
            binding.blockNotification.setDebouncedClickListener {
                onClick?.onClickedNotification()
            }
//            binding.blockViewProfile.setDebouncedClickListener {
//                onClick?.onClickedViewInfo()
//            }
            binding.blockSearch.setDebouncedClickListener {
                onClick?.onClickedSearch()
            }
//            binding.titleViewProfile.setText(
//                com.gg.gapo.core.ui.GapoStrings.messenger_direct_chat_settings_view_profile
//            )
            if (conversation.partner?.isSystem() == true && !conversation.isBotCommand()) {
                binding.blockNotification.gone()
            }
//            if (conversation.partner?.isSystem() == true) {
//                binding.blockViewProfile.gone()
//            }
            binding.blockSearch.visibility = if (showSearchMessage) View.VISIBLE else View.GONE
        }
    }

    override fun unbind(binding: ItemMessengerChatSettingsDirectInfoBinding) {
    }

    override fun getDefaultLayout(): Int {
        return R.layout.item_messenger_chat_settings_direct_info
    }
}
