package com.gg.gapo.messenger.presentation.features.conversation.models

import android.content.Context
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.ItemFriendOnlineBinding
import com.gg.gapo.messenger.domain.models.User
import com.gg.gapo.messenger.helper.Constant
import com.gg.gapo.messenger.helper.extensions.loadCircleUserAvatar
import com.gg.gapo.messenger.helper.utils.ChatUtils
import com.gg.gapo.messenger.presentation.bases.DataBindingModel
import com.gg.gapo.messenger.presentation.common.components.OnlineStatusView
import com.gg.gapo.messenger.presentation.features.search.FriendItemHandler

@EpoxyModelClass
abstract class ConversationFriendsModel : DataBindingModel<ItemFriendOnlineBinding>() {
    @EpoxyAttribute
    var user: User? = null

    @EpoxyAttribute
    var lastSeen: Long? = 0

    private val handler: FriendItemHandler = FriendItemHandler()

    override fun bind(binding: ItemFriendOnlineBinding, context: Context) {
        val name = ChatUtils.getSmartName(user?.getUserName().orEmpty(), Constant.MAX_NAME_LENGTH)
        binding.name.text = name
        binding.imageAvatar.loadCircleUserAvatar(user, GapoAutoDimens._64dp)
        binding.status.setType(type = OnlineStatusView.OnlineStatusType.ONLINE, lastSeen)
        binding.blockUser.setDebouncedClickListener {
            user?.let { user ->
                handler.gotoChatDetail(context, user.id, user.displayName, user.avatar)
            }
        }
    }

    override fun unbind(binding: ItemFriendOnlineBinding) {
    }

    override fun getDefaultLayout(): Int {
        return R.layout.item_friend_online
    }
}
