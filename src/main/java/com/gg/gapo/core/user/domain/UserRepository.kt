package com.gg.gapo.core.user.domain

import com.gg.gapo.core.user.domain.model.UserProfileFieldModel
import com.gg.gapo.core.user.domain.model.UserProfileModel
import com.gg.gapo.core.user.domain.model.request.UpdateUserProfileRequestModel
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @since 22/12/2021
 */
interface UserRepository {

    /**
     * set user id
     */
    fun setUserId(userId: String)

    /**
     * get user id
     */
    fun getUserId(): String

    /**
     * fetch profile
     */
    suspend fun fetchUserProfile(): UserProfileModel?

    /**
     * update user profile
     */
    suspend fun updateUserProfile(request: UpdateUserProfileRequestModel): UserProfileModel?

    /**
     * emit thay đổi khi cached user profile được update
     */
    fun observeUserProfile(): Flow<UserProfileModel>

    suspend fun fetchProfileFieldCustom(): UserProfileFieldModel?

    suspend fun fetchWorkspaceFieldCustom(): UserProfileFieldModel?

    /**
     * clear các thông tin của user
     */
    fun clear()
}
