package com.gg.gapo.core.feed.post.dto

import com.gg.gapo.core.feed.post.domain.PostGroupModel
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 09/03/2022
 */
data class PostGroupDto(
    @SerializedName("id") val id: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("cover") val cover: String?,
    @SerializedName("cover_thumb_pattern") val coverThumbPattern: String?,
    @SerializedName("privacy") val privacy: String?,
    @SerializedName("owner_id") val ownerId: String?,
    @SerializedName("alias") val alias: String?,
    @SerializedName("role") val role: String?,
    @SerializedName("post_need_approve") val postNeedApproval: Boolean?,
    @SerializedName("is_default") val isDefaultOfWs: Boolean?,
    @SerializedName("type") val type: Int?
)

internal fun PostGroupDto.mapToDomain(): PostGroupModel? {
    val privacy = PostGroupModel.Privacy.getByPrivacy(privacy)
    return if (id.isNullOrEmpty() || name.isNullOrEmpty() || privacy == null) null
    else PostGroupModel(
        id = id,
        name = name,
        cover = cover,
        coverThumbPattern = coverThumbPattern,
        privacy = privacy,
        ownerId = ownerId.orEmpty(),
        alias = alias,
        role = PostGroupModel.Role.getByRole(role) ?: PostGroupModel.Role.GUEST,
        postNeedApproval = postNeedApproval ?: false,
        isDefaultOfWs = isDefaultOfWs ?: false,
        type = PostGroupModel.Type.getByType(type) ?: PostGroupModel.Type.NORMAL
    )
}
