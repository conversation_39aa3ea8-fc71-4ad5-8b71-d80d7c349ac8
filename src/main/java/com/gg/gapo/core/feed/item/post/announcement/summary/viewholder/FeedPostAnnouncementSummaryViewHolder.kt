package com.gg.gapo.core.feed.item.post.announcement.summary.viewholder

import android.os.Bundle
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.gg.gapo.core.feed.databinding.FeedPostAnnouncementSummaryItemBinding
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.post.announcement.summary.model.FeedPostAnnouncementSummaryViewData
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.utilities.view.setDebouncedClickListener

/**
 * <AUTHOR>
 * @since 06/12/2021
 */
internal class FeedPostAnnouncementSummaryViewHolder(
    private val binding: FeedPostAnnouncementSummaryItemBinding,
    private val feedImageLoader: FeedImageLoader
) : FeedViewHolder<FeedPostAnnouncementSummaryItemBinding, FeedPostAnnouncementSummaryViewData>(
    binding
) {

    override fun onBind(item: FeedPostAnnouncementSummaryViewData, bundle: Bundle?) {
        binding.layoutRoot.setDebouncedClickListener {
            item.interactor.onPostClickOnView(item.postId)
        }
        binding.buttonMore.setDebouncedClickListener {
            item.interactor.onAnnouncementPostClickOnOptions(item.postId)
        }
        if (bundle == null) {
            binding.layoutRoot.updateLayoutParams<FrameLayout.LayoutParams> {
                if (item.context !is FeedPostAnnouncementSummaryViewData.AnnouncementContext.FeedAnnouncement) {
                    setMargins(
                        space12dp,
                        space8dp * 2,
                        0,
                        space8dp * 2
                    )
                } else {
                    setMargins(space12dp, space12dp, space12dp, space12dp)
                }
            }
            binding.layoutInformation.isVisible =
                item.context !is FeedPostAnnouncementSummaryViewData.AnnouncementContext.FeedAnnouncement
            setUser(item.user)
            loadMedia(item.media)
            setContent(item.title, item.description)
            setDate(item.date)
            setViewCount(item.seenCount)
            binding.layoutNewBadge.isVisible = item.isNewBadgeVisible
            binding.buttonMore.isVisible =
                item.context !is FeedPostAnnouncementSummaryViewData.AnnouncementContext.FeedAnnouncement
        } else {
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.USER_CHANGED_EXTRA)) {
                setUser(item.user)
            }
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.MEDIA_CHANGED_EXTRA)) {
                loadMedia(item.media)
            }
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.CONTENT_CHANGED_EXTRA)) {
                setContent(item.title, item.description)
            }
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.DATE_CHANGED_EXTRA)) {
                setDate(item.date)
            }
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.SEEN_COUNT_CHANGED_EXTRA)) {
                setViewCount(item.seenCount)
            }
            if (bundle.containsKey(FeedPostAnnouncementSummaryViewData.NEW_BADGE_VISIBLE_CHANGED_EXTRA)) {
                binding.layoutNewBadge.isVisible = item.isNewBadgeVisible
            }
        }
    }

    override fun onViewRecycled() {
        feedImageLoader.clear(binding.imageAvatar)
        feedImageLoader.clear(binding.imageMedia)
        binding.layoutRoot.removeOnClickListener()
        binding.buttonMore.removeOnClickListener()
    }

    private fun setUser(user: FeedPostAnnouncementSummaryViewData.User) {
        binding.textUserName.text = user.displayName
        binding.imageAvatar.loadCircle(
            feedImageLoader,
            user.avatar,
            user.avatarPlaceHolder
        )
    }

    private fun setContent(title: String?, description: String) {
        binding.textTitle.isVisible = !title.isNullOrEmpty()
        binding.textTitle.text = title
        binding.textDescription.text = if (title.isNullOrEmpty() && description.isEmpty()) GapoGlobalResources.getString(
            GapoStrings.feeds_post_announcement_post_has_not_description
        ) else description
    }

    private fun setDate(date: String) {
        binding.textDate.text = date
    }

    private fun setViewCount(viewCount: Long) {
        binding.textViewCount.text = viewCount.toString()
    }

    private fun loadMedia(media: FeedPostAnnouncementSummaryViewData.Media?) {
        when {
            media == null -> {
                binding.cardMedia.isVisible = false
            }
            media.media.isNullOrEmpty() -> {
                binding.cardMedia.isVisible = true
                binding.imageMedia.scaleType = ImageView.ScaleType.CENTER_INSIDE
                binding.imageMedia.setImageResource(media.icon)
            }
            else -> {
                binding.cardMedia.isVisible = true
                binding.imageMedia.scaleType = ImageView.ScaleType.CENTER_CROP
                feedImageLoader.load(media.media)
                    .centerCrop()
                    .dontAnimate()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .priority(Priority.IMMEDIATE)
                    .into(binding.imageMedia)
            }
        }
    }
}
