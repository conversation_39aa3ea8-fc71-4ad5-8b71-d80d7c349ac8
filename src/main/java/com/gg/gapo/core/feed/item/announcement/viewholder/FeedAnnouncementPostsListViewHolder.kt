package com.gg.gapo.core.feed.item.announcement.viewholder

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.databinding.FeedAnnouncementPostsListItemBinding
import com.gg.gapo.core.feed.item.FeedItemDecoration
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.announcement.adapter.FeedAnnouncementPagerAdapter
import com.gg.gapo.core.feed.item.announcement.adapter.FeedAnnouncementPagerAdapter.Companion.MAX_PAGE
import com.gg.gapo.core.feed.item.announcement.adapter.FeedAnnouncementPostAdapter
import com.gg.gapo.core.feed.item.announcement.decoration.FeedAnnouncementPostDecoration
import com.gg.gapo.core.feed.item.announcement.model.FeedAnnouncementPostsListViewData
import com.gg.gapo.core.feed.item.announcement.model.FeedPostAnnouncementSliderViewData
import com.gg.gapo.core.feed.item.post.announcement.summary.model.FeedPostAnnouncementSummaryViewData
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import kotlinx.coroutines.CoroutineDispatcher

/**
 * <AUTHOR>
 * @since 06/12/2021
 */
internal class FeedAnnouncementPostsListViewHolder(
    private val binding: FeedAnnouncementPostsListItemBinding,
    feedImageLoader: FeedImageLoader,
    differCoroutineDispatcher: CoroutineDispatcher
) : FeedViewHolder<FeedAnnouncementPostsListItemBinding, FeedAnnouncementPostsListViewData>(
    binding
),
    FeedItemDecoration.ShowDivider {

    private val announcementPostAdapter =
        FeedAnnouncementPostAdapter(context, feedImageLoader, differCoroutineDispatcher)

    private val announcementPosts = mutableListOf<FeedPostAnnouncementSliderViewData>()

    private val announcementPagerAdapter =
        FeedAnnouncementPagerAdapter(feedImageLoader, announcementPosts)

    init {
        binding.listAnnouncementPosts.apply {
            itemAnimator = null
            isNestedScrollingEnabled = false
            addItemDecoration(FeedAnnouncementPostDecoration(context))
            layoutManager = LinearLayoutManager(context)
        }
    }

    override fun onBind(item: FeedAnnouncementPostsListViewData, bundle: Bundle?) {
        binding.textAll.setDebouncedClickListener {
            item.interactor.onAnnouncementPostClickOnViewAll()
        }
        if (bundle == null) {
            setByContext(item, bundle)
            binding.textAll.setText(GapoStrings.feeds_announcement_post_view_all)
        } else {
            if (bundle.containsKey(FeedAnnouncementPostsListViewData.CONTEXT_CHANGED_EXTRA) ||
                bundle.containsKey(FeedAnnouncementPostsListViewData.ANNOUNCEMENT_POSTS_CHANGED_EXTRA)
            ) {
                setByContext(item, bundle)
            }
        }
        // Hiển thị tối đa 5 bài announcement trên feed nên buttin ViewAll luôn hiện
        // binding.textAll.isVisible = item.isTextAllVisible

        binding.textAll.isVisible = true
    }

    override fun onViewRecycled() {
        binding.textAll.removeOnClickListener()
    }

    override fun onDetachedFromRecyclerView() {
        onViewRecycled()
        binding.listAnnouncementPosts.adapter = null
    }

    private fun setByContext(
        item: FeedAnnouncementPostsListViewData,
        bundle: Bundle?
    ) {
        when (item.context) {
            is FeedPostAnnouncementSummaryViewData.AnnouncementContext.FeedFollow -> {
                binding.listAnnouncementPosts.isVisible = false
                binding.viewPagerRoot.isVisible = true
                binding.textHeader.setText(GapoStrings.feeds_announcement_post_workspace_title)
                binding.imageHeader.setImageResource(R.drawable.feed_announcement_post_ic_announcement_workspace_52dp)
                binding.textHeader.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    if (item.context.isAdminWS) GapoDrawables.ic16_fill_questionmark_circle else GapoDrawables.ic16_fill_informationmark_circle,
                    0
                )
                // Chỉ click vào icon Info
                binding.textHeader.setOnTouchListener(
                    View.OnTouchListener { _, event ->
                        if (event.action == MotionEvent.ACTION_UP) {
                            val textLocation = IntArray(2)
                            binding.textHeader.getLocationOnScreen(textLocation)

                            if (event.rawX >= textLocation[0] +
                                binding.textHeader.width - binding.textHeader.totalPaddingRight
                            ) {
                                item.interactor.onAnnouncementPostClickOnUserGuide(item.context.isAdminWS)
                                return@OnTouchListener true
                            }
                        }
                        true
                    }
                )
                // view pager
                announcementPosts.clear()
                item.posts.take(MAX_PAGE).forEach {
                    announcementPosts.add(
                        FeedPostAnnouncementSliderViewData.cloneFromSummaryViewData(it)
                    )
                }

                if (binding.announcementViewPager.adapter == null) {
                    binding.announcementViewPager.adapter = announcementPagerAdapter
                }
                binding.announcementViewPager.offscreenPageLimit = 5
                binding.dotsIndicator.attachTo(binding.announcementViewPager)
                announcementPagerAdapter.updateData(announcementPosts)
            }

            is FeedPostAnnouncementSummaryViewData.AnnouncementContext.GroupDetails -> {
                binding.listAnnouncementPosts.isVisible = false
                binding.viewPagerRoot.isVisible = true
                binding.textHeader.setText(GapoStrings.feeds_announcement_post_group_title)
                binding.imageHeader.setImageResource(R.drawable.feed_announcement_post_ic_announcement_group_52dp)
                binding.textHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                binding.textHeader.setOnClickListener(null)

                // view pager
                announcementPosts.clear()
                item.posts.take(MAX_PAGE).forEach {
                    announcementPosts.add(
                        FeedPostAnnouncementSliderViewData.cloneFromSummaryViewData(it)
                    )
                }
                if (binding.announcementViewPager.adapter == null) {
                    binding.announcementViewPager.adapter = announcementPagerAdapter
                }
                binding.announcementViewPager.offscreenPageLimit = 5
                binding.dotsIndicator.attachTo(binding.announcementViewPager)
                announcementPagerAdapter.updateData(announcementPosts)
            }

            else -> {
                binding.listAnnouncementPosts.isVisible = true
                binding.viewPagerRoot.isVisible = false
                binding.textHeader.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                binding.textHeader.setOnClickListener(null)
                if (binding.listAnnouncementPosts.adapter == null) {
                    binding.listAnnouncementPosts.adapter = announcementPostAdapter
                }
                announcementPostAdapter.submitList(item.posts.take(FeedAnnouncementPostsListViewData.ANNOUNCEMENT_POSTS_MAX))
            }
        }
    }
}
