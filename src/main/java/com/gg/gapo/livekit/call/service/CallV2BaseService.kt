package com.gg.gapo.livekit.call.service

import android.os.Build
import androidx.lifecycle.LifecycleService
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.feature.call.domain.usecase.GetLastCallIdUseCase
import com.gg.gapo.feature.call.notification.CallNotifier
import com.gg.gapo.livekit.call.center.CallCenterV2
import com.gg.gapo.livekit.call.logger.CallLoggerV2
import org.koin.android.ext.android.inject

/**
 * <AUTHOR>
 */
internal abstract class CallV2BaseService : LifecycleService() {

    protected val callCenter by inject<CallCenterV2>()

    protected val getLastCallIdUseCase by inject<GetLastCallIdUseCase>()

    protected val callLogger by inject<CallLoggerV2>()

    protected val userManager by inject<UserManager>()

    protected val myId: String
        get() = userManager.userId

    override fun onCreate() {
        super.onCreate()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CallNotifier.createCallInProgressNotificationChannel(this)
        }
    }
}
