package com.gg.gapo.livekit.call.presentation.logger

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.feature.call.R

@AppDeepLink(value = ["call_v2/logger"])
class CallLoggerActivity : GapoThemeBaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.call_logger_activity)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }
}
