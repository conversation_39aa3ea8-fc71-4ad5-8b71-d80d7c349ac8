<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".presentation.features.settings.members.GroupChatMembersFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgPrimary">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/titleBar"
            style="@style/GapoTextStyle.BodyLarge.Bold"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center_vertical|left"
            android:paddingStart="@dimen/_48dp"
            android:text="@string/messenger_group_chat_settings_privacy_settings_view_member_title"
            android:textColor="@color/contentPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/imvBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imvBack"
            style="@style/MessengerButtonBackStyle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/txtContinue"
            style="@style/GapoTextStyle.BodyLarge.Bold"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingStart="@dimen/_12dp"
            android:paddingEnd="@dimen/_12dp"
            android:text="@string/messenger_group_chat_settings_privacy_settings_add_member"
            android:visibility="gone"
            tools:visibility="visible"
            android:textColor="@color/accentPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/imvBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorLine"
            app:layout_constraintBottom_toTopOf="@id/swipeLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/search_view" />

        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_8dp"
            android:layout_toEndOf="@+id/imvBack"
            android:background="@drawable/bg_search_view_border"
            android:clickable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:theme="@style/SearchView"
            android:visibility="gone"
            app:closeIcon="@drawable/ic_messenger_close"
            app:iconifiedByDefault="false"
            app:layout_constraintBottom_toTopOf="@id/divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleBar"
            app:queryBackground="@android:color/transparent"
            app:queryHint="@string/shared_search"
            app:searchIcon="@drawable/ic_search"
            tools:visibility="visible" />

        <com.gg.gapo.core.ui.swiperefreshlayout.GapoSwipeRefreshLayout
            android:id="@+id/swipeLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider"
                tools:listitem="@layout/item_messenger_chat_settings_view_member" />
        </com.gg.gapo.core.ui.swiperefreshlayout.GapoSwipeRefreshLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>