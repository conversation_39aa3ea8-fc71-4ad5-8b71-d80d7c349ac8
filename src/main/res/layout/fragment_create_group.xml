<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".presentation.features.folder.selection.SelectionConversationFragment">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_bottom_sheet_white_round_right_left"
        android:orientation="vertical">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/titleBar"
                style="@style/GapoTextStyle.BodyLarge.Bold"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/_40dp"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/_40dp"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/_16dp"
                android:paddingTop="@dimen/_10dp"
                android:paddingBottom="@dimen/_10dp"
                android:text="@string/messenger_folder_list_creation_add"
                android:textColor="@color/contentPrimary"
                app:layout_constraintBottom_toTopOf="@+id/searchBar"
                app:layout_constraintEnd_toStartOf="@id/txtContinue"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />



            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imvBack"
                style="@style/MessengerButtonBackStyle"
                android:layout_height="?android:attr/actionBarSize"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txtContinue"
                style="@style/GapoTextStyle.BodyLarge.Bold"
                android:layout_width="wrap_content"
                android:layout_height="?android:attr/actionBarSize"
                android:gravity="center"
                android:paddingStart="@dimen/_12dp"
                android:paddingTop="@dimen/_10dp"
                android:paddingEnd="@dimen/_16dp"
                android:paddingBottom="@dimen/_10dp"
                android:text="@string/messenger_group_chat_settings_privacy_settings_add_member"
                android:textColor="@color/accentPrimary"
                app:layout_constraintBottom_toBottomOf="@+id/titleBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/titleBar"
                app:layout_constraintTop_toTopOf="parent" />

            <com.gg.gapo.core.ui.input.GapoSearchEditText
                android:id="@+id/searchBar"
                style="@style/GapoInput.Search"
                android:layout_width="0dp"
                android:layout_height="@dimen/_36dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:clickable="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:theme="@style/SearchView"
                android:textCursorDrawable="@drawable/bg_messenger_cursor_input"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/titleBar"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="@dimen/_16dp">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/blockMembers"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/searchView">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewSelected"
                    android:layout_width="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:orientation="horizontal"
                    android:layout_height="@dimen/_110dp"
                    android:nestedScrollingEnabled="false"
                    app:layout_constraintBottom_toTopOf="@+id/recyclerView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/searchView"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_messenger_user_selected_horizontal" />

                <com.airbnb.epoxy.EpoxyRecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/recyclerViewSelected"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_messenger_user_selection_vertical" />
            </androidx.appcompat.widget.LinearLayoutCompat>
        </FrameLayout>


    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>