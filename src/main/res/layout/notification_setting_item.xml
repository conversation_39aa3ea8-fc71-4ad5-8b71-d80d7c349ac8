<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="settingNotification"
            type="com.gg.gapo.feature.notification.presentation.settings.model.SettingNotificationViewData" />

        <variable
            name="viewModel"
            type="com.gg.gapo.feature.notification.presentation.settings.viewmodel.SettingNotificationViewModel" />

        <variable
            name="listener"
            type="com.gg.gapo.feature.notification.presentation.settings.listener.SettingNotificationActionsListener" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_64dp"
        android:onClick="@{(view)->listener.onClick(settingNotification, view)}">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/image_icon_setting_notification"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_16dp"
            android:tint="@color/contentPrimary"
            app:settingNotification="@{settingNotification}" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_title_setting_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_toStartOf="@+id/switch_setting_notification"
            android:layout_toEndOf="@+id/image_icon_setting_notification"
            android:text="@{settingNotification.title}"
            android:textColor="@color/contentPrimary"
            android:textSize="@dimen/_16sp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_setting_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_16dp"
            android:onClick="@{(view) -> listener.onChangeSwitch(settingNotification, view)}"
            style="@style/GapoSelection.Switch"
            android:visibility="@{settingNotification.visibleSwitch ? View.VISIBLE : View.GONE}"
            app:valueSwitch="@{settingNotification.value}" />

    </RelativeLayout>

</layout>