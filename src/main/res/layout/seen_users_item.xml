<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:clipChildren="false"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.gg.gapo.core.ui.image.GapoAvatarImageView
        android:id="@+id/image_avatar"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:clickable="false"
        android:focusable="false" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_weight="1"
        android:clickable="false"
        android:ellipsize="end"
        android:focusable="false"
        android:lines="1"
        android:singleLine="true"
        android:textAppearance="@style/GapoTextAppearance.BodyLarge"
        android:textColor="@color/contentPrimary"
        tools:text="Display Name" />

    <FrameLayout
        android:id="@+id/layout_button_action"
        android:layout_width="@dimen/_124dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:background="@drawable/reacted_users_bg_chat_friend"
        android:clickable="true"
        android:focusable="true">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/button_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="@dimen/_8dp"
            android:text="@string/feeds_post_users_chat_friend_label"
            android:textAppearance="@style/GapoTextAppearance.HeadingSmall"
            android:textColor="?accentWorkSecondary"
            app:drawableStartCompat="@drawable/ic24_fill_bubble_ellipse_3dot"
            app:drawableTint="?accentWorkSecondary" />

    </FrameLayout>

</androidx.appcompat.widget.LinearLayoutCompat>