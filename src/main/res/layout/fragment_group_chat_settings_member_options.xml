<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />
        <import type="com.gg.gapo.messenger.domain.models.UserRole" />

        <variable
            name="acl"
            type="com.gg.gapo.messenger.presentation.features.managers.ChatACL" />

        <variable
            name="user"
            type="com.gg.gapo.messenger.domain.models.User" />

        <variable
            name="threadId"
            type="String" />

        <variable
            name="isFeatureMessengerEnabled"
            type="Boolean" />

        <variable
            name="isFeatureCallEnabled"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16dp"
        android:background="@drawable/bg_bottom_sheet_white_round_right_left"
        >

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/GapoTextStyle.BodyLarge.Bold"
            android:id="@+id/text_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="@string/common_share_bottom_sheet_dialog_title"
            android:textColor="@color/contentPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_divider_1"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            android:layout_marginTop="@dimen/_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_title" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/blockViewProfile"
            style="@style/Messenger.ShareItem"
            android:visibility="gone"
            android:drawableLeft="@drawable/ic_chat_settings_member_view_profile"
            android:text="@string/messenger_group_chat_settings_member_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_1" />

        <View
            android:id="@+id/view_divider_2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:visibility="gone"
            android:background="@color/transparent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/blockViewProfile" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/blockChat"
            style="@style/Messenger.ShareItem"
            android:drawableLeft="@drawable/ic_chat_settings_member_chat"
            android:text="@string/messenger_group_chat_settings_member_send_mess"
            android:visibility="@{ isFeatureMessengerEnabled ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_2" />

        <View
            android:id="@+id/view_divider_3"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            android:visibility="@{(acl.askAssignAdmin(threadId) &amp;&amp; user.role == UserRole.MEMBER) &amp;&amp; !user.isBot() ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/blockChat" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/assignAdmin"
            style="@style/Messenger.ShareItem"
            android:drawableLeft="@drawable/ic_chat_settings_member_grant_permisson_admin"
            android:text="@string/messenger_group_chat_settings_assign_admin_title"
            android:visibility="@{(acl.askAssignAdmin(threadId) &amp;&amp; user.role == UserRole.MEMBER  &amp;&amp; !user.isBot()) ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_3"
            />

        <View
            android:id="@+id/view_divider_4"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            android:visibility="@{(acl.askRemoveAdmin(threadId) &amp;&amp; user.role == UserRole.ADMIN  &amp;&amp; !user.isBot()) ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/assignAdmin" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/removeAdmin"
            style="@style/Messenger.ShareItem"
            android:visibility="@{(acl.askRemoveAdmin(threadId) &amp;&amp; user.role == UserRole.ADMIN   &amp;&amp; !user.isBot()) ? View.VISIBLE : View.GONE }"
            android:drawableLeft="@drawable/ic_chat_settings_member_remove_permisson_admin"
            android:text="@string/group_member_popup_remove_admin_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_4" />

        <View
            android:id="@+id/view_divider_5"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            android:visibility="@{ acl.isOwner(threadId)  &amp;&amp; !user.isBot()? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/removeAdmin" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/assignOwner"
            style="@style/Messenger.ShareItem"
            android:drawableLeft="@drawable/ic_owner_group_chat"
            android:visibility="@{ acl.isOwner(threadId)  &amp;&amp; !user.isBot()  ? View.VISIBLE : View.GONE }"
            android:text="@string/messenger_group_chat_settings_assign_owner_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_5" />

        <View
            android:id="@+id/view_divider_common_groups"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/assignOwner" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_view_common_groups"
            style="@style/Messenger.ShareItem"
            android:visibility="@{ !user.isBot() ? View.VISIBLE : View.GONE }"
            android:text="@string/messenger_common_groups_view"
            app:drawableStartCompat="@drawable/ic24_line15_3people"
            android:drawableLeft="@drawable/ic_chat_settings_member_remove"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view_divider_common_groups" />

        <View
            android:id="@+id/view_divider_6"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:background="@color/transparent"
            android:visibility="@{ acl.askRemoveUser(threadId,user)  &amp;&amp; !user.isBot() ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_view_common_groups" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_view_audio_call"
            style="@style/Messenger.ShareItem"
            android:visibility="@{ (!user.isBot() &amp;&amp; isFeatureCallEnabled) ? View.VISIBLE : View.GONE }"
            android:text="@string/messenger_audio_call"
            app:drawableStartCompat="@drawable/ic24_line15_phone"
            android:drawableLeft="@drawable/ic24_line15_phone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view_divider_6" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_view_video_Call"
            style="@style/Messenger.ShareItem"
            android:visibility="@{ (!user.isBot() &amp;&amp; isFeatureCallEnabled) ? View.VISIBLE : View.GONE }"
            android:text="@string/messenger_video_call"
            app:drawableStartCompat="@drawable/ic24_line15_sidecamera"
            android:drawableLeft="@drawable/ic24_line15_sidecamera"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_audio_call" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/blockRemove"
            style="@style/Messenger.ShareItem"
            android:textColor="@color/delete"
            android:visibility="@{ acl.askRemoveUser(threadId,user) ? View.VISIBLE : View.GONE }"
            android:drawableLeft="@drawable/ic_chat_settings_member_remove"
            android:text="@string/messenger_group_chat_settings_member_remove"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_view_video_Call" />

        <View
            android:id="@+id/view_divider_7"
            android:background="@color/transparent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0_5dp"
            android:visibility="@{ acl.askRemoveUser(threadId,user) ? View.VISIBLE : View.GONE }"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/blockRemove" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>