{"formatVersion": "1.1", "component": {"group": "dev.fluttercommunity.plus.connectivity", "module": "connectivity_plus_release", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "connectivity_plus_release-1.0.aar", "url": "connectivity_plus_release-1.0.aar", "size": 9910, "sha512": "411f8973d48e83998fc390965a3d9b541a5c4f683c4275202de3d241f44a942c2afc501fb25c1e424fe285f49aa466bbbc1ab0698dc0cac6843eba9936cd07f7", "sha256": "da4dca91a6c2bd5b86db2b83c32867e0637b6facc4cd4a3c492a062030074f64", "sha1": "4a67c88b053097aca7ced9ea5f7cb667b30d6635", "md5": "a5d0686ede4f625c6681759f9fd32756"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "connectivity_plus_release-1.0.aar", "url": "connectivity_plus_release-1.0.aar", "size": 9910, "sha512": "411f8973d48e83998fc390965a3d9b541a5c4f683c4275202de3d241f44a942c2afc501fb25c1e424fe285f49aa466bbbc1ab0698dc0cac6843eba9936cd07f7", "sha256": "da4dca91a6c2bd5b86db2b83c32867e0637b6facc4cd4a3c492a062030074f64", "sha1": "4a67c88b053097aca7ced9ea5f7cb667b30d6635", "md5": "a5d0686ede4f625c6681759f9fd32756"}]}]}