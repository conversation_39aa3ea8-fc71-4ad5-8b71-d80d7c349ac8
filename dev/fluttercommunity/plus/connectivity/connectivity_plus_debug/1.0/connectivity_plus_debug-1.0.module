{"formatVersion": "1.1", "component": {"group": "dev.fluttercommunity.plus.connectivity", "module": "connectivity_plus_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "connectivity_plus_debug-1.0.aar", "url": "connectivity_plus_debug-1.0.aar", "size": 10295, "sha512": "98bbec10acad83ef2d795352f679aad7c690e65021436722a1e8cb33d15aaf0d3abc919c5b3737f71e2d044a5432ea9fa7c94c7768775070b64db20e339ecc43", "sha256": "415aaccf7b739b846d5aa159f988f49f72e2a3387e5ecfb378d060e4a87bef62", "sha1": "97de537e18492042936c5f49b57801ec9d00adcc", "md5": "30a550b8687a9ad7e9c70b6e67313a6a"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "connectivity_plus_debug-1.0.aar", "url": "connectivity_plus_debug-1.0.aar", "size": 10295, "sha512": "98bbec10acad83ef2d795352f679aad7c690e65021436722a1e8cb33d15aaf0d3abc919c5b3737f71e2d044a5432ea9fa7c94c7768775070b64db20e339ecc43", "sha256": "415aaccf7b739b846d5aa159f988f49f72e2a3387e5ecfb378d060e4a87bef62", "sha1": "97de537e18492042936c5f49b57801ec9d00adcc", "md5": "30a550b8687a9ad7e9c70b6e67313a6a"}]}]}