{"formatVersion": "1.1", "component": {"group": "dev.fluttercommunity.plus.packageinfo", "module": "package_info_plus_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "package_info_plus_debug-1.0.aar", "url": "package_info_plus_debug-1.0.aar", "size": 8265, "sha512": "402f24d94612bc84fbcdc73db52aeaeb911c22c63c6b307de2404d796efbcac7296411ee0715f98847184ac8be8a5c79571f4ef802f9267dd6b1f346f5a6d10d", "sha256": "c7f5e0df022d806eae8f74edafa339dc745335b0870babdb64f9706bab4c46ab", "sha1": "6487766b29340fa423a0d95770ccc2c1c149f664", "md5": "c40e9745c559c7965e6477ccc45d0a14"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.7.22"}}], "files": [{"name": "package_info_plus_debug-1.0.aar", "url": "package_info_plus_debug-1.0.aar", "size": 8265, "sha512": "402f24d94612bc84fbcdc73db52aeaeb911c22c63c6b307de2404d796efbcac7296411ee0715f98847184ac8be8a5c79571f4ef802f9267dd6b1f346f5a6d10d", "sha256": "c7f5e0df022d806eae8f74edafa339dc745335b0870babdb64f9706bab4c46ab", "sha1": "6487766b29340fa423a0d95770ccc2c1c149f664", "md5": "c40e9745c559c7965e6477ccc45d0a14"}]}]}