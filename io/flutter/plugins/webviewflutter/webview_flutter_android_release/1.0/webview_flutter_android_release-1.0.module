{"formatVersion": "1.1", "component": {"group": "io.flutter.plugins.webviewflutter", "module": "webview_flutter_android_release", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}], "files": [{"name": "webview_flutter_android_release-1.0.aar", "url": "webview_flutter_android_release-1.0.aar", "size": 148038, "sha512": "08e5cf95a7a23df403c4083a83b381ebf614eafe945f9fc422a389bcda7626a3d51660556ba61c275e1341c2de3b2f9b005f1c062eaa97635f158adf9c1cff12", "sha256": "ae3435a61b59dc01492ee36dfb4c454fc93f6e2bbd45f4fac26624c0e928ce14", "sha1": "e12a361218e1eec6abe8d91cfdfb40697705456e", "md5": "5095d8e0bacddcf01161e9fd6bc186c1"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-edd8546116457bdf1c5bdfb13ecb9463d2bb5ed4"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.7.1"}}, {"group": "androidx.webkit", "module": "webkit", "version": {"requires": "1.11.0"}}], "files": [{"name": "webview_flutter_android_release-1.0.aar", "url": "webview_flutter_android_release-1.0.aar", "size": 148038, "sha512": "08e5cf95a7a23df403c4083a83b381ebf614eafe945f9fc422a389bcda7626a3d51660556ba61c275e1341c2de3b2f9b005f1c062eaa97635f158adf9c1cff12", "sha256": "ae3435a61b59dc01492ee36dfb4c454fc93f6e2bbd45f4fac26624c0e928ce14", "sha1": "e12a361218e1eec6abe8d91cfdfb40697705456e", "md5": "5095d8e0bacddcf01161e9fd6bc186c1"}]}]}