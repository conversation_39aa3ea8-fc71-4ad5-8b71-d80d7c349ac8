plugins {
    alias libs.plugins.android.library
    alias libs.plugins.kotlin.android
    alias libs.plugins.kotlin.kapt
    alias libs.plugins.kotlin.parcelize
    alias libs.plugins.ksp
    alias libs.plugins.androidx.navigation
}

apply from: "$rootDir/android-flavors.gradle"

android {
    namespace "com.gg.gapo.authentication"

    defaultConfig {
        ksp {
            arg("deepLink.incremental", "true")
            arg("deepLink.customAnnotations", "com.gg.gapo.core.navigation.AppDeepLink|com.gg.gapo.core.navigation.WebDeepLink")
        }
    }

    buildFeatures {
        dataBinding true
        viewBinding true
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation modulePath(projects.analytic)

    implementation modulePath(projects.coreGapo)
    implementation modulePath(projects.coreEventBus)
    implementation modulePath(projects.coreUi)
    implementation modulePath(projects.coreUtilities)

    stagingImplementation stagingModulePath(projects.coreNavigation)
    uatImplementation uatModulePath(projects.coreNavigation)
    saasProductionImplementation saasProductionModulePath(projects.coreNavigation)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.coreNavigation)

    implementation libs.kotlin.stdlib
    implementation libs.kotlin.coroutines.core
    implementation libs.kotlin.coroutines.android

    implementation libs.androidx.appcompat
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.core.ktx
    implementation libs.androidx.constraintlayout
    implementation libs.google.android.material

    implementation libs.androidx.work.runtime.ktx

    implementation libs.androidx.navigation.ui.ktx
    implementation libs.androidx.navigation.fragment.ktx

    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.androidx.lifecycle.common.java8

    implementation libs.gms.playServices.auth

    implementation libs.retrofit

    implementation libs.microsoft.msal

    implementation libs.glide

    implementation libs.jwtdecode

    implementation libs.koin.android

    implementation libs.autoDimens

    implementation libs.deeplinkdispatch
    ksp libs.deeplinkdispatch.processor

    implementation platform(libs.firebase.bom)
    implementation libs.firebase.messaging

    implementation libs.eventbus

    implementation libs.gson
    implementation libs.timber
    implementation libs.jwtdecode
    implementation libs.libphonenumber

    implementation libs.gms.playServices.auth
    implementation libs.gms.playServices.auth.apiPhone
    implementation libs.recaptcha

    testImplementation libs.junit
    testImplementation libs.androidx.test.junit
    testImplementation libs.androidx.test.core
    testImplementation libs.androidx.test.rules
}