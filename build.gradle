plugins {
    alias libs.plugins.android.library
    alias libs.plugins.kotlin.android
    alias libs.plugins.kotlin.kapt
    alias libs.plugins.kotlin.parcelize
    alias libs.plugins.ksp
    alias libs.plugins.androidx.navigation
}

apply plugin: "realm-android"

apply from: "$rootDir/android-flavors.gradle"
apply from: "$rootDir/android-epoxy-config.gradle"

android {
    namespace "com.gg.gapo.messenger"

    defaultConfig {

        vectorDrawables.useSupportLibrary = true

        ksp {
            arg("room.incremental", "true")
            arg("room.expandProjection", "true")
            arg("deepLink.incremental", "true")
            arg("deepLink.customAnnotations", "com.gg.gapo.core.navigation.AppDeepLink|com.gg.gapo.core.navigation.WebDeepLink")
        }
    }

    buildFeatures {
        dataBinding true
        viewBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.kotlinCompiler.get()
    }

    sourceSets {
        main {
            res.srcDirs = ['src/main/res',
                           'src/main/res-v2']
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation modulePath(projects.coreGapo)
    implementation modulePath(projects.coreEventBus)
    implementation modulePath(projects.coreUi)
    implementation modulePath(projects.coreUtilities)

    implementation modulePath(projects.libraryGallery)
    implementation modulePath(projects.libraryPanelAndroidx)

    implementation modulePath(projects.sharedPollVote)

    stagingImplementation stagingModulePath(projects.coreNavigation)
    uatImplementation uatModulePath(projects.coreNavigation)
    saasProductionImplementation saasProductionModulePath(projects.coreNavigation)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.coreNavigation)

    stagingImplementation stagingModulePath(projects.featureSticker)
    uatImplementation uatModulePath(projects.featureSticker)
    saasProductionImplementation saasProductionModulePath(projects.featureSticker)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureSticker)

    stagingImplementation stagingModulePath(projects.featureTooltips)
    uatImplementation uatModulePath(projects.featureTooltips)
    saasProductionImplementation saasProductionModulePath(projects.featureTooltips)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureTooltips)

    implementation libs.kotlin.stdlib
    implementation libs.kotlin.coroutines.core
    implementation libs.kotlin.coroutines.android

    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    implementation libs.google.android.material
    implementation libs.androidx.activity.ktx
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.swiperefreshlayout
    implementation libs.androidx.recyclerview
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.browser
    implementation libs.androidx.exifinterface
    implementation libs.androidx.localbroadcastmanager

    implementation libs.androidx.paging3.runtime

    implementation libs.androidx.emoji2.views
    implementation libs.emoji.google

    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.androidx.lifecycle.common.java8

    implementation libs.androidx.navigation.ui.ktx
    implementation libs.androidx.navigation.fragment.ktx

    implementation libs.androidx.work.runtime.ktx
    implementation libs.androidx.work.rxjava2

    implementation libs.androidx.room.runtime
    implementation libs.androidx.room.ktx
    implementation libs.androidx.room.rxjava2
    implementation libs.androidx.localbroadcastmanager
    ksp libs.androidx.room.compiler

    implementation libs.realm.kotlin.extensions

    implementation platform(libs.firebase.bom)
    implementation libs.firebase.crashlytics

    implementation libs.reactivex.rx2Java
    implementation libs.reactivex.rx2Kotlin
    implementation libs.reactivex.rx2Android
    implementation libs.reactivex.rx3Java

    implementation libs.koin.android
    implementation libs.koin.androidx.workmanager

    implementation libs.retrofit

    implementation libs.okhttp

    implementation libs.epoxy
    implementation libs.epoxy.databinding
    kapt libs.epoxy.processor

    implementation libs.eventbus

    implementation libs.glide
    implementation libs.glide.webp
    implementation libs.glide.compose

    implementation libs.deeplinkdispatch
    ksp libs.deeplinkdispatch.processor

    implementation libs.sentry

    implementation libs.autoDimens

    implementation libs.exoplayer.core
    implementation libs.exoplayer.ui
    implementation libs.exoplayer.hls

    implementation libs.markwon.core
    implementation libs.markwon.ext.strikeThrough
    implementation libs.markwon.ext.tables
    implementation libs.markwon.ext.taskList
    implementation libs.markwon.linkify
    implementation libs.markwon.inline.parser

    implementation libs.circleimageview
    implementation libs.sheetmenu
    implementation libs.timber
    implementation libs.spinKit
    implementation libs.flexbox
    implementation libs.elasticviews
    implementation libs.materialprogressbar
    implementation libs.swipeActionView
    implementation libs.hauler
    implementation libs.keyboardvisibilityevent
    implementation libs.ucrop
    implementation(libs.swipelayout) { artifact { type = 'aar' } }
    implementation libs.utilcodex
    implementation libs.materialPopupMenu
    implementation libs.axrLottie
    implementation libs.customtabshelper
    implementation libs.autocomplete
    implementation libs.libphonenumber
    implementation libs.spyglass
    implementation libs.spotlight
    implementation libs.joda.time
    implementation libs.compressor
    implementation libs.audioRecordView

    implementation libs.simpleCommons
    implementation(libs.patternLockview) {
        exclude group: 'com.andrognito.patternlockview', module: 'patternlockview'
    }
    implementation(libs.rtlDuolingoViewpager) {
        exclude group: 'com.duolingo.open', module: 'rtl-viewpager'
    }

    implementation libs.balloon
    implementation libs.slidableActivity

    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.compose.activity
    implementation libs.androidx.compose.ui
    implementation libs.androidx.compose.ui.graphics
    implementation libs.androidx.compose.ui.tooling.preview
    implementation libs.androidx.compose.material3
    debugImplementation libs.androidx.compose.ui.tooling
    debugImplementation libs.androidx.compose.ui.test.manifest

    testImplementation libs.junit
    testImplementation libs.androidx.test.junit
    testImplementation libs.androidx.test.core
    testImplementation libs.androidx.test.rules

}
